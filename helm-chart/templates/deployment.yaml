apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "chart.name" . }}
  labels:
    app: {{ include "chart.name" . }}
    
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ include "chart.name" . }}
      
  template:
    metadata:
      labels:
        app: {{ include "chart.name" . }} 
        release: {{ .Release.Name }}
      annotations:
        timestamp: {{ now | quote }}
        proxy.istio.io/config: |
          proxyLogLevel: debug
          componentLogLevel: "http:debug,connection:trace"
    spec:
      # Add deployment specific settings
      containers:
        - name: {{ include "chart.name" . }}
{{- if .Values.image.repository }}
          # The image is used from your registry
          image: "{{ .Values.image.repository }}/{{ include "chart.name" . }}:{{ .Values.image.tag | default .Chart.Version }}"        
{{ else }}
          # The local image is used
          image: "{{ include "chart.name" . }}:{{ .Values.image.tag }}"
{{- end }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            # context path is defined in values file and injected as env variable
            # env variable has higher priority than "context-path" in application.yml
            - name: SERVER_SERVLET_CONTEXTPATH
              value: "{{ .Values.contextPath }}"

            # This env is defined in the docker file
            - name: JAVA_OPTIONS
              value: {{ .Values.container.java_opts }}
              
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "chart.name" . }}-db-secret
                  key: password

            - name: IAM_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ include "chart.name" . }}-iam-secret
                  key: client-secret

            - name: AZURE_COMMUNICATION_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "chart.name" . }}-email-secret
                  key: access-key

{{- if .Values.resources }}
          resources: {{- toYaml .Values.resources | nindent 12 }}
{{- end }}

          livenessProbe:
            httpGet:
              path: {{ .Values.contextPath }}/actuator/health/liveness
              port: {{ .Values.application.server.port }}
            initialDelaySeconds: {{ .Values.container.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.container.livenessProbe.periodSeconds }}
          readinessProbe:
            httpGet:
              path: {{ .Values.contextPath }}/actuator/health/readiness
              port: {{ .Values.application.server.port }}
            initialDelaySeconds: {{ .Values.container.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.container.readinessProbe.periodSeconds }}
          lifecycle:
            preStop:
              exec:
                command: ["sh", "-c", "sleep 10"]

          volumeMounts:
            - name: config-volume
              mountPath: /app/config

      volumes:
      - name: config-volume
        configMap:
          name: {{ include "chart.name" . }}-configmap

{{- if .Values.imagePullSecrets }}
{{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
{{- end }}
{{- end }}
