# The needed resource (RAM, CPU) 
container:
  java_opts: "-Xms256m -Xmx1024m"

  readinessProbe:
    initialDelaySeconds: 20
    periodSeconds: 10

  livenessProbe:
    initialDelaySeconds: 60
    periodSeconds: 30

resources:
  requests:
    memory: 256Mi
    cpu: 512m

  limits:
    memory: 712Mi
    cpu: 1000m

# Application configuration (this will override the config inside the docker image)
application:
  server:
    port: 8000

  database:
    url: **************************************************
    username: postgres
    password: pO5t$zum

  iam:
    endpoint: http://keycloak:8080
    realm: smaile
    client:
      id: smaile-be
      secret: B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB

  email:
    azure:
      endpoint: "https://smaile-communication-svc-ap.asiapacific.communication.azure.com/"
      accesskey: "67NJJbYI1z8WMQaYsleqfTPmTqe1ZyPzfEjOfwd4CBaVY3QTwW6sJQQJ99BHACULyCpQD44pAAAAAZCSwZiI"  # Will be provided via secret
      sender-address: "<EMAIL>"
    support:
      email: "<EMAIL>"
      phone: "******-0123"

  app:
    login-url: "https://smaile.egs-dev.site/login"

# The needed secrets that already exists in this cluster
secret:
  database:
    name: scs-name--egs-database-secret

  keycloak:
    name: scs-name--egs-app-client-secret

  email:
    name: scs-name--egs-email-secret

# define the domain (it will be http://subDomain.baseDomain) that other service in the cluster can access to
# The domain can be replaced by your real domain and then it can be accessed on your internet
global:
  baseDomain: minikube
  subDomain: api.smaile

clusterDomain: svc.cluster.local

contextPath: /api/v1

image:
  repository:
  pullPolicy:

