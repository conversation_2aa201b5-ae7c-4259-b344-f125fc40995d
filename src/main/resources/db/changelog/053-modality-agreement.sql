--liquibase formatted sql
--changeset audit-team:053-modality-agreements

create table modality_agreements (
    modality_id UUID not null,
    agreement_id UUID not null,
    date_created TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by   <PERSON><PERSON><PERSON><PERSON>(255)             NOT NULL,
    last_updated TIMES<PERSON>MP WITH TIME ZONE NOT NULL,
    updated_by   VA<PERSON>HAR(255)             NOT NULL,

    primary key (modality_id, agreement_id),
    constraint uq_modality_agreement unique (modality_id, agreement_id)
);

--ROLLBACK DROP TABLE modality_agreements;