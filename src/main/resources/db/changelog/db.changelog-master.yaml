databaseChangeLog:
  - include:
      - file: db/changelog/001-initial-schema.sql
  - include:
      - file: db/changelog/002-initial-data.sql
  - include:
      - file: db/changelog/003-insurance-companies-table.sql
  - include:
      - file: db/changelog/004-add-code-column-to-organization.sql
  - include:
      - file: db/changelog/005-initial-data-2.sql
  - include:
      - file: db/changelog/006-add-permissions.sql
  - include:
      - file: db/changelog/007-add-organization-permissions.sql
  - include:
      - file: db/changelog/008-add-crud-user-permissions-for-all-admin-type.sql
  - include:
      - file: db/changelog/009-drop-legal-id-column.sql
  - include:
      - file: db/changelog/010-create-professional-table.sql
  - include:
      - file: db/changelog/011-update-role-entities.sql
  - include:
      - file: db/changelog/012-update-table-user-add-org-id.sql
  - include:
      - file: db/changelog/013-tpa-organizations-table.sql
  - include:
      - file: db/changelog/014-add-permits-professional-resource.sql
  - include:
      - file: db/changelog/015-add-medical-providers-table.sql
  - include:
      - file: db/changelog/016-add-create-org-permission-for-all-admin.sql
  - include:
      - file: db/changelog/017-add-default-smaile-admin-role.sql
  - include:
      - file: db/changelog/018-add-is_deleted-field-on-organization.sql
  - include:
      - file: db/changelog/019-add-org-permission-to-mp-admin.sql
  - include:
      - file: db/changelog/020-add-country-column-to-MP.sql
  - include:
      - file: db/changelog/021-drop-market-column-from-tpa.sql
  - include:
      - file: db/changelog/022-drop-column-roleid-for-user.sql
  - include:
      - file: db/changelog/023-add-colum-pro-lob.sql
  - include:
      - file: db/changelog/024-add-professional-contact-info.sql
  - include:
      - file: db/changelog/025-add-not-null-constraint-on-org-parent-id.sql
  - include:
      - file: db/changelog/026-make-user-status-not-null.sql
  - include:
      - file: db/changelog/027-create-diagnosis-table.sql
  - include:
      - file: db/changelog/029-create-specialties-table.sql
  - include:
      - file: db/changelog/030-create-required-proofs-table.sql
  - include:
      - file: db/changelog/031-create-table-agreement.sql
  - include:
      - file: db/changelog/032-add-providers-professionals-table.sql
  - include:
      - file: db/changelog/033-create-procedure-table.sql
  - include:
      - file: db/changelog/034-rename-proofs-and-add-junction-tables.sql
  - include:
      - file: db/changelog/035-refactor-entities-to-relationships.sql
  - include:
      - file: db/changelog/036-drop-country-column-from-procedure.sql
  - include:
      - file: db/changelog/038-add-username-column.sql
  - include:
      - file: db/changelog/038-refine-user-predefined-data.sql
  - include:
      - file: db/changelog/037-add-table-professional-specialty.sql
  - include:
      - file: db/changelog/039-add-username-not-null.sql
  - include:
      - file: db/changelog/040-migrate-name-to-type-and-drop-name-column.sql
  - include:
      - file: db/changelog/041-add-username-unique.sql
  - include:
      - file: db/changelog/042-rename-proof-name-to-code.sql
  - include:
      - file: db/changelog/043-create-modalities-table.sql
  - include:
      - file: db/changelog/044-add-modality-permissions.sql
  - include:
      - file: db/changelog/045-ic-agreement-task.sql
  - include:
      - file: db/changelog/046-add-insured-table.sql
  - include:
      - file: db/changelog/047-alter-insured-datetime-to-date.sql
  - include:
      - file: db/changelog/048-drop-surname-column.sql
  - include:
      - file: db/changelog/049-add-last-login-column.sql
  - include:
      - file: db/changelog/050-add-org-attachments-table.sql
  - include:
      - file: db/changelog/046-create-modality-procedures-table.sql
  - include:
      - file: db/changelog/047-create-modality-procedure-diagnosis-table.sql
  - include:
      - file: db/changelog/051-add-deleted-flat-to-org-attachment.sql
  - include:
      - file: db/changelog/052-create-partitioned-audit-log.sql
  - include:
      - file: db/changelog/053-modality-agreement.sql
  - include:
      - file: db/changelog/054-remove-column-professional-specialty.sql
  - include:
      - file: db/changelog/055-alter-modality-procedures-table-drop-network-type-column.sql