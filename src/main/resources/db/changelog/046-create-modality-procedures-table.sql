--liquibase formatted sql
--changeset phiendq:046-create-modality-procedures-table.sql

CREATE TABLE modality_procedures
(
    id                          UUID                     NOT NULL
        CONSTRAINT modality_procedures_pk
            PRIMARY KEY,
    modality_id                 UUID                     NOT NULL
        CONSTRAINT modality_procedures_modalities_id_fk
            REFERENCES modalities,
    procedure_id                UUID                     NOT NULL
        CONSTRAINT modality_procedures_procedure_id_fk
            REFERENCES procedure,
    procedure_code              VARCHAR(255)             NOT NULL,
    network_type                VARCHAR(50)              NOT NULL,
    override_frequency          VARCHAR(10),
    override_market_cost_amount NUMERIC(18, 2),
    age_group                   VARCHAR(100),
    effective_date          DATE                     NOT NULL,
    cancellation_date       DATE,
    date_created                TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by                  VARCHAR(255)             NOT NULL,
    last_updated                TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by                  VARCHAR(255)
);

CREATE TABLE modality_procedure_coverage
(
    id                    UUID                     NOT NULL
        CONSTRAINT modality_procedure_coverage_pk
            PRIMARY KEY,
    modality_procedure_id UUID                     NOT NULL
        CONSTRAINT modality_procedure_coverage_modality_procedures_id_fk
            REFERENCES modality_procedures,
    network_type          VARCHAR(50)              NOT NULL,
    fixed_amount          NUMERIC(18, 2),
    percentage            INTEGER,
    deductible_amount     NUMERIC(18, 2),
    date_created          TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by            VARCHAR(255),
    last_updated          TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by            VARCHAR(255)
);

CREATE INDEX modality_procedure_coverage_modality_procedure_id_index
    ON modality_procedure_coverage (modality_procedure_id);


--rollback DROP INDEX IF EXISTS modality_procedure_coverage_modality_procedure_id_index;
--rollback DROP TABLE modality_procedure_coverage;
--rollback DROP TABLE modality_procedures;

