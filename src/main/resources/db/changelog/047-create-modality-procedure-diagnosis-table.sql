--liquibase formatted sql
--changeset phiendq:047-create-modality-procedure-diagnosis-table.sql

CREATE TABLE modality_diagnosis
(
    id                          uuid
        constraint modality_diagnosis_pk
            primary key,
    modality_id                 uuid                     not null
        constraint modality_diagnosis_modalities_id_fk
            references modalities,
    diagnosis_id                uuid                     not null
        constraint modality_diagnosis_diagnosis_id_fk
            references diagnosis,
    diagnosis_code              VARCHAR(255)             NOT NULL,
    network_type                VARCHAR(50)              NOT NULL,
    type                        VARCHAR(50)              NOT NULL,
    override_frequency          VARCHAR(10),
    override_market_cost_amount NUMERIC(18, 2),
    effective_date              DATE                     NOT NULL,
    cancellation_date           DATE,
    date_created                TIMESTAMP WITH TIME ZONE NOT NULL,
    created_by                  VA<PERSON><PERSON><PERSON>(255)             NOT NULL,
    last_updated                TIMESTAMP WITH TIME ZONE NOT NULL,
    updated_by                  VA<PERSON>HAR(255)
);

CREATE TABLE modality_procedure_diagnosis
(
    modality_procedure_id UUID NOT NULL
        CONSTRAINT modality_procedure_diagnosis_modality_procedures_id_fk
            REFERENCES modality_procedures,
    modality_diagnosis_id UUID NOT NULL
        CONSTRAINT modality_procedure_diagnosis_modality_diagnosis_id_fk
            REFERENCES modality_diagnosis,
    CONSTRAINT modality_procedure_diagnosis_pk
        PRIMARY KEY (modality_procedure_id, modality_diagnosis_id)
);

CREATE INDEX modality_procedure_diagnosis_modality_diagnosis_id_index
    ON modality_procedure_diagnosis (modality_diagnosis_id);

--rollback DROP INDEX IF EXISTS modality_procedure_diagnosis_modality_diagnosis_id_index;
--rollback DROP TABLE modality_procedure_diagnosis;
--rollback DROP TABLE modality_diagnosis;
