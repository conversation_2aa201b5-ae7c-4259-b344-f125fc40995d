--liquibase formatted sql
--changeset longnh:050-add-org-attachments

create table org_attachments
(
    id              uuid
        constraint org_attachments_pk
            primary key,
    organization_id uuid
        constraint org_attachments_organizations_id_fk
            references organizations,
    object_url      varchar not null,
    file_name       varchar(255) not null,
    version         integer,
    date_created    timestamptz,
    created_by      varchar(100),
    last_updated    timestamptz,
    updated_by      varchar(100)
);

--rollback drop table org_attachments;