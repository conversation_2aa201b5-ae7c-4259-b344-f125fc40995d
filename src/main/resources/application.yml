server:
  servlet:
    context-path: /api/v1

spring:
  profiles:
    default: dev

  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 30000 # 30 seconds
      maximum-pool-size: 20
      minimum-idle: 10
      idle-timeout: 300000 # 5 minutes
      max-lifetime: 1200000 # 20 minutes
      leak-detection-threshold: 60000 # 60 seconds
  jpa:
    hibernate:
      ddl-auto: none
    open-in-view: false
    properties:
      hibernate:
        format_sql: false
        show_sql: false
        jdbc:
          batch_size: 25
          order_inserts: true
          order_updates: true
          lob:
            non_contextual_creation: true
        id:
          new_generator_mappings: true

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.yaml
    default-schema: public
    enabled: true
    contexts: default

cors:
  allowed-origins:
    - http://localhost:4180
    - https://ui.smaile.egs-dev.site/
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true
  max-age: 3600

# The IAM configuration for the application
# they are used to admin client creating user in keycloak
iam:
  endpoint: ${IAM_ENDPOINT:http://localhost:8080}
  realm: ${IAM_REALM:smaile}
  client-id: ${IAM_CLIENT_ID:smaile-be}
  client-secret: ${IAM_CLIENT_SECRET:B7ca4tWTJdSK4g1L8yq0mi7xmISNydiB}

file-storage:
  azure:
    account-name: smailebackend
    account-key: ****************************************************************************************
    container-name: smaileupload

# Email configuration
email:
  azure:
    endpoint: ${AZURE_COMMUNICATION_ENDPOINT:https://smaile-communication-svc-ap.asiapacific.communication.azure.com/}
    access-key: ${AZURE_COMMUNICATION_ACCESS_KEY}
    sender-address: ${AZURE_SENDER_ADDRESS:<EMAIL>}
  support:
    email: ${SUPPORT_EMAIL:<EMAIL>}
    phone: ${SUPPORT_PHONE:******-0123}

# Application configuration
app:
  login-url: ${LOGIN_URL:https://ui.smaile-test.egs-dev.site}
  audit:
    enabled: true
    cache-enabled: true

springdoc:
  packages-to-scan: com.smaile.health.controller
  pathsToMatch: /, /**
  swagger-ui:
    enabled: true
    path: /docs

logging:
  level:
    com.smaile.health: INFO
    org.springframework.security: TRACE
    liquibase: WARN
    root: WARN