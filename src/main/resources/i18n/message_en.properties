
msg.general.success=Success
msg.general.error=Error
msg.general.unknown-error=Unknown error occurred

# Error message
error.permission-denied=You don't have permission to perform this action
error.not-implemented=The function has not been implemented yet

# Ic Agreement Company errors
error.ic-agreement.id-not-found=Ic Agreement not found
error.ic-agreement.existing-ic-and-agreement=Ic Agreement with this Insurance Company and Agreement already exists

# Agreement Company errors
error.agreement.mp-not-found=Medical provider not found
error.agreement.time-range-invalid=Time range is invalid
error.agreement.contract-id-duplicated=Contract id is duplicated
error.agreement.not-found=Agreement not found
error.agreement.not-authorized-with-mp=Can not create agreement with MP {0}
# Insurance Company errors
error.insurance-company.not-found=Insurance Company not found
error.insurance-company.name-duplicate=Insurance Company with this name already exists
# TPA errors
error.tpa.not-found=TPA organization not found
error.tpa.name-duplicate=TPA organization with this name already exists
error.tpa.insufficient-permissions-to-create=You don't have permission to create TPA organizations. Only IC and SUPER_SMAILE organizations can create TPAs.

# Organization errors
error.organization.not-found=Organization not found
error.organization.name-duplicate=Organization with this name already exists
error.organization.parent-not-found=Cannot find parent organization
error.organization.attachment-not-found=Cannot find organization's attachment with the provided info
error.user-organization.not-found=User organization not found
# User errors
error.user.not-found=User not found
error.user.is-deleted=User is deleted and cannot be used
error.user.email-duplicate=User with this email already exists
#Insured errors
error.insured.active-insured-exist=Cannot create Insured due to active entity existing
error.insured.not-found=Cannot find any existing Insured with the provided info
error.insured.update-denied=Cannot update inactive Insured
error.insured.status-date-not-in-range=Status effective date is not in range of coverage window
error.insured.not-active=Insured is not in active status
error.insured.effective-date-not-in-range=Effective date not in range of coverage
error.insured.coverage-range-invalid=Invalid coverage begin date/end date range

# Role errors
error.role.not-found=Role not found
# Diagnosis errors
error.diagnosis.not-found=Diagnosis not found
error.diagnosis.code-duplicate=Diagnosis with this code already exists
error.diagnosis.inactive=Diagnosis is inactive
error.diagnosis.already-inactive=Diagnosis is already inactive
# Procedure errors
error.procedure.not-found=Procedure not found
error.procedure.code-duplicate=Procedure with this code already exists
error.procedure.inactive=Procedure is inactive
error.procedure.already-inactive=Procedure is already inactive

# General errors
error.validation=Validation error occurred
error.internal-server=Internal server error occurred

# Message
msg.file.not-uploaded=File not uploaded
msg.professional.duplicate-email=Professional with this email {0} already exists
msg.professional.duplicate-username=Professional with this username {0} already exists
msg.professional.duplicate-license-id=Professional with this license ID {0} already exists
msg.professional.invalid-status=Professional with Invalid status to this action
msg.sample-with-param=Hello {0}
msg.insurance-company.created=Insurance Company created successfully
msg.insurance-company.updated=Insurance Company updated successfully
msg.insurance-company.approved=Insurance Company approved successfully
msg.insurance-company.deleted=Insurance Company deleted successfully
msg.insurance-company.deactivated=Insurance Company deactivated successfully
msg.insurance-company.operation.failed=Insurance Company operation failed
msg.user.operation.failed=User operation failed
msg.insurance-company.cannot-update-inactive=Cannot update inactive insurance company
msg.insurance-company.already-inactive=Insurance Company is already inactive
msg.insurance-company.already-approved=Insurance Company is already approved
msg.organization.updated=Organization updated successfully
msg.organization.created=Organization created successfully
msg.organization.retrieved=Organization retrieved successfully
msg.organization.deleted=Organization deleted successfully
msg.insurance-company.retrieved=Insurance Company retrieved successfully
# TPA messages
msg.tpa.created=TPA organization created successfully
msg.tpa.updated=TPA organization updated successfully
msg.tpa.retrieved=TPA organization retrieved successfully
msg.tpa.deactivated=TPA organization deactivated successfully
msg.tpa.operation.failed=TPA organization operation failed
msg.tpa.cannot-update-inactive=Cannot update inactive TPA organization
msg.tpa.already-inactive=TPA organization is already inactive
# Diagnosis messages
msg.diagnosis.created=Diagnosis created successfully
msg.diagnosis.updated=Diagnosis updated successfully
msg.diagnosis.retrieved=Diagnosis retrieved successfully
msg.diagnosis.deactivated=Diagnosis deactivated successfully
msg.diagnosis.operation.failed=Diagnosis operation failed
# Procedure messages
msg.procedure.created=Procedure created successfully
msg.procedure.updated=Procedure updated successfully
msg.procedure.retrieved=Procedure retrieved successfully
msg.procedure.deactivated=Procedure deactivated successfully
msg.procedure.operation.failed=Procedure operation failed
# Specialty messages
msg.speciality.not-found=One or more specialties not found
msg.speciality.inactive=One or more specialties are inactive
msg.speciality.created=Specialty created successfully
msg.speciality.updated=Specialty updated successfully
msg.speciality.retrieved=Specialty retrieved successfully
msg.speciality.deleted=Specialty deleted successfully
msg.speciality.operation.failed=Specialty operation failed
# Specialty errors
error.speciality.not-found=Specialty not found
error.speciality.inactive=Specialty is inactive
error.speciality.code-already-exists=Specialty with this code already exists
error.speciality.operation-failed=Specialty operation failed

# Modality messages
msg.modality.created=Modality created successfully
msg.modality.updated=Modality updated successfully
msg.modality.retrieved=Modality retrieved successfully

# Modality errors
error.modality.code-already-exists=Modality with this code already exists
error.modality.not-found=Modality not found
error.modality.modality-code-is-immutable=Modality code is immutable
error.modality.network-type-dont-match=Network type don't match modality network type

# Modality diagnosis messages
msg.modality-diagnosis.created=Modality diagnosis created successfully
msg.modality-diagnosis.removed=Modality diagnosis removed successfully 

# Modality procedure messages
msg.modality-procedure.created=Link procedure to modality successfully
msg.modality-procedure.updated=Updated linked procedure successfully
msg.modality-procedure.removed=Remove procedure from modality successfully
msg.modality-procedure.retrieved=Retrieved procedure from modality successfully

# Modality procedure errors
error.modality-procedure.not-found=Modality Procedure not found.
error.modality-procedure.exists=Procedure already added to this modality.
# Modality diagnosis errors
error.modality-diagnosis.exists=Diagnosis already in this modality
error.modality-diagnosis.not-found=Diagnosis not found in this modality
error.modality-diagnosis.unlink-not-allowed=Only allowed to remove orphaned diagnosis in the modality

# Proof errors
error.proof.not-found=Proof not found
error.proof.inactive=Proof is inactive
error.proof.code-duplicate=Proof with this code already exists
error.proof.date-range-invalid=Cancellation date cannot be before effective date

# Proof messages
msg.proof.not-found=One or more proofs not found
msg.proof.inactive=One or more proofs are inactive
msg.proof.created=Proof created successfully
msg.proof.updated=Proof updated successfully
msg.proof.retrieved=Proof retrieved successfully
msg.proof.deleted=Proof deleted successfully
msg.proof.operation.failed=Proof operation failed
msg.proof.cannot-update-inactive=Cannot update inactive proof
msg.proof.already-inactive=Proof is already inactive
