package com.smaile.health.util;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

public final class DateTimeUtils {

    public static final DateTimeFormatter ISO_OFFSET_DATE_TIME = DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    public static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private DateTimeUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static OffsetDateTime now() {
        return OffsetDateTime.now(ZoneOffset.UTC);
    }

    public static OffsetDateTime nowInZone(ZoneId zoneId) {
        return OffsetDateTime.now(zoneId);
    }

    public static String formatToIso(OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.format(ISO_OFFSET_DATE_TIME) : null;
    }

    public static String formatToDate(OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMAT) : null;
    }

    public static String formatToTimestamp(OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.format(TIMESTAMP_FORMAT) : null;
    }

    public static OffsetDateTime truncateToDay(OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.truncatedTo(ChronoUnit.DAYS) : null;
    }

    public static OffsetDateTime truncateToHour(OffsetDateTime dateTime) {
        return dateTime != null ? dateTime.truncatedTo(ChronoUnit.HOURS) : null;
    }

    public static boolean isBefore(OffsetDateTime date1, OffsetDateTime date2) {
        return date1 != null && date2 != null && date1.isBefore(date2);
    }

    public static boolean isAfter(OffsetDateTime date1, OffsetDateTime date2) {
        return date1 != null && date2 != null && date1.isAfter(date2);
    }

    public static long daysBetween(OffsetDateTime start, OffsetDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.DAYS.between(start, end);
    }

    public static OffsetDateTime addDays(OffsetDateTime dateTime, long days) {
        return dateTime != null ? dateTime.plusDays(days) : null;
    }

    public static OffsetDateTime addHours(OffsetDateTime dateTime, long hours) {
        return dateTime != null ? dateTime.plusHours(hours) : null;
    }

    public static boolean isBetween(Instant target, Instant start, Instant end) {
        Objects.requireNonNull(target, "target cannot be null");
        Objects.requireNonNull(start, "start cannot be null");
        Objects.requireNonNull(end, "end cannot be null");
        return !target.isBefore(start) && !target.isAfter(end);
    }
}