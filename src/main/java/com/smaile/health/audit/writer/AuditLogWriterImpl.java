package com.smaile.health.audit.writer;

import com.smaile.health.audit.AuditContext;
import com.smaile.health.audit.AuditLogEntry;
import com.smaile.health.audit.AuditLogWriter;
import com.smaile.health.constants.EntityType;
import com.smaile.health.domain.AuditLog;
import com.smaile.health.repository.AuditLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Writes audit logs directly to the database.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/09/07
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AuditLogWriterImpl implements AuditLogWriter {

    private final AuditLogRepository auditLogRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,
            isolation = Isolation.READ_COMMITTED)
    public void writeAuditLog(AuditLogEntry entry) {
        if (AuditContext.isAuditInProgress()) {
            log.debug("Skipping audit write - circular audit detected");
            return;
        }

        try {
            AuditContext.setAuditInProgress(true);

            AuditLog auditLog = new AuditLog();
            auditLog.setCreatedAt(entry.getCreatedAt());
            auditLog.setEntityName(EntityType.valueOf(entry.getEntityName()));
            auditLog.setEntityId(entry.getEntityId());
            auditLog.setOperation(entry.getOperation());
            auditLog.setOldValues(entry.getOldValuesJson());
            auditLog.setNewValues(entry.getNewValuesJson());
            auditLog.setChangedColumns(entry.getChangedColumns());
            auditLog.setCreatedBy(entry.getCreatedBy());

            auditLogRepository.save(auditLog);

            log.debug("Successfully wrote audit log for entity: {} id: {} operation: {} changedBy: {}",
                    entry.getEntityName(), entry.getEntityId(), entry.getOperation(), entry.getCreatedBy());

        } catch (Exception e) {
            log.error("Failed to write audit log for entity: {} id: {} operation: {} changedBy: {} - Error: {}",
                    entry.getEntityName(), entry.getEntityId(), entry.getOperation(), entry.getCreatedBy(),
                    e.getMessage(), e);
        } finally {
            AuditContext.setAuditInProgress(false);
        }
    }

}
