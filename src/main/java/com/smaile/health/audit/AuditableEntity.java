package com.smaile.health.audit;

import com.smaile.health.constants.EntityType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation for marking entities as auditable.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/09/07
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AuditableEntity {

    String entityName() default "";

    EntityType entityEnum() default EntityType.NONE;

    boolean enabled() default true;

}