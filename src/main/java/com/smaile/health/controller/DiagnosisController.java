package com.smaile.health.controller;

import com.smaile.health.model.DiagnosisDTO;
import com.smaile.health.model.DiagnosisSummaryDTO;
import com.smaile.health.model.request.CreateDiagnosisRequestDTO;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.DiagnosisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/diagnoses", produces = MediaType.APPLICATION_JSON_VALUE)
@AllArgsConstructor
@Tag(name = "Diagnosis Controller", description = "CRUD operations for diagnosis records")
public class DiagnosisController {

    private final DiagnosisService diagnosisService;

    @GetMapping("/query")
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    @Operation(summary = "Search diagnoses", description = "Search diagnoses with pagination and optional filters")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Diagnoses retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Invalid parameters or validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<DiagnosisDTO>>> searchDiagnoses(
            @Parameter(description = "Filter - searches in code and description fields (case-insensitive)",
                    example = "K02")
            @RequestParam(required = false) String filter,
            @PageableDefault(size = 20, sort = "code") Pageable pageable) {

        PageResponse<DiagnosisDTO> pageResponse = PageResponse.of(diagnosisService.search(filter, pageable));
        return ResponseEntity.ok(SmaileApiResponse.success(pageResponse));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    @Operation(summary = "Get diagnosis by ID", description = "Retrieve a specific diagnosis by its UUID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Diagnosis retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "404", description = "Diagnosis not found")
    })
    public ResponseEntity<BaseResponse<DiagnosisDTO>> getDiagnosis(
            @Parameter(description = "Diagnosis UUID")
            @PathVariable UUID id) {

        DiagnosisDTO diagnosis = diagnosisService.get(id);
        return ResponseEntity.ok(BaseResponse.of(diagnosis));
    }

    @PostMapping
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    @Operation(summary = "Create diagnosis", description = "Create a new diagnosis record")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Diagnosis created successfully"),
            @ApiResponse(responseCode = "400", description = "Bad request - validation errors"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "409", description = "Conflict - duplicate diagnosis code")
    })
    public ResponseEntity<BaseResponse<UUID>> createDiagnosis(
            @Valid @RequestBody CreateDiagnosisRequestDTO requestDTO) {

        UUID createdId = diagnosisService.create(requestDTO);
        return ResponseEntity.ok(BaseResponse.of(createdId));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    @Operation(summary = "Update diagnosis", description = "Update an existing diagnosis record")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Diagnosis updated successfully"),
            @ApiResponse(responseCode = "400", description = "Bad request - validation errors"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "404", description = "Diagnosis not found"),
            @ApiResponse(responseCode = "409", description = "Conflict - duplicate diagnosis code")
    })
    public ResponseEntity<SmaileApiResponse<UUID>> updateDiagnosis(
            @Parameter(description = "Diagnosis UUID")
            @PathVariable UUID id,
            @Valid @RequestBody DiagnosisDTO diagnosisDTO) {

        diagnosisService.update(id, diagnosisDTO);
        return ResponseEntity.ok(SmaileApiResponse.success(id));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    @Operation(summary = "Delete diagnosis", description = "Deactivate a diagnosis (soft delete)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Diagnosis deactivated successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "404", description = "Diagnosis not found")
    })
    public ResponseEntity<SmaileApiResponse<UUID>> deleteDiagnosis(
            @Parameter(description = "Diagnosis UUID")
            @PathVariable UUID id) {

        diagnosisService.delete(id);
        return ResponseEntity.ok(SmaileApiResponse.success(id));
    }

    @GetMapping("/summaries")
    @PreAuthorize("hasRole('SUPER_SMAILE_ADMIN')")
    @Operation(summary = "Get all diagnosis summaries",
            description = "Retrieve all active diagnoses with only id and code for use in dropdowns and selection lists")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Diagnosis summaries retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<List<DiagnosisSummaryDTO>>> getAllDiagnosisSummaries() {
        List<DiagnosisSummaryDTO> summaries = diagnosisService.getAllSummaries();
        return ResponseEntity.ok(SmaileApiResponse.success(summaries));
    }

}
