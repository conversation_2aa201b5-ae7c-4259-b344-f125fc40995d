package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.model.SpecialityDTO;
import com.smaile.health.model.request.CreateSpecialityRequestDTO;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.SpecialityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/specialities", produces = MediaType.APPLICATION_JSON_VALUE)
@AllArgsConstructor
@Tag(name = "Speciality", description = "Speciality management APIs")
public class SpecialityController {

    private final SpecialityService specialityService;
    private final I18nService i18nService;

    @GetMapping("/query")
    @Operation(summary = "Search specialties", description = "Search specialties with pagination and filters")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Specialties retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Invalid parameters or validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<SpecialityDTO>>> searchSpecialities(
            @Parameter(description = "Filter - searches in name or description fields (case-insensitive)",
                    example = "General Dentists")
            @RequestParam(required = false) String filter,
            @PageableDefault(size = 20)
            @SortDefault.SortDefaults({
                    @SortDefault(sort = "lastUpdated", direction = org.springframework.data.domain.Sort.Direction.DESC)
            }) Pageable pageable) {
        var specialties = specialityService.search(filter, pageable);
        return ResponseEntity.ok(SmaileApiResponse.success(PageResponse.of(specialties)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get specialty by ID", description = "Retrieve a specialty by its ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Specialty retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "404", description = "Specialty not found")
    })
    public ResponseEntity<SmaileApiResponse<SpecialityDTO>> getSpeciality(
            @Parameter(description = "Specialty ID", required = true)
            @PathVariable final UUID id) {
        final SpecialityDTO speciality = specialityService.get(id);
        return ResponseEntity.ok(SmaileApiResponse.success(speciality));
    }

    @PostMapping
    @Operation(summary = "Create specialty", description = "Create a new specialty")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Specialty created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "409", description = "Specialty with same code already exists")
    })
    public ResponseEntity<SmaileApiResponse<UUID>> createSpeciality(
            @Parameter(
                    description = "Specialty data to create",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = CreateSpecialityRequestDTO.class)
                    )
            )
            @RequestBody @Valid final CreateSpecialityRequestDTO requestDTO) {
        final UUID createdId = specialityService.create(requestDTO);
        String message = i18nService.getMessage(MessageKey.SPECIALITY_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(createdId, message));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update specialty", description = "Update an existing specialty")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Specialty updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "404", description = "Specialty not found"),
            @ApiResponse(responseCode = "409", description = "Specialty with same code already exists")
    })
    public ResponseEntity<SmaileApiResponse<Void>> updateSpeciality(
            @Parameter(description = "Specialty ID", required = true)
            @PathVariable final UUID id,
            @Parameter(
                    description = "Updated specialty data",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SpecialityDTO.class)
                    )
            )
            @RequestBody @Valid final SpecialityDTO specialityDTO) {
        specialityService.update(id, specialityDTO);
        String message = i18nService.getMessage(MessageKey.SPECIALITY_UPDATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(null, message));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete specialty", description = "Delete a specialty (soft delete)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Specialty deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - SUPER_SMAILE_ADMIN role required"),
            @ApiResponse(responseCode = "404", description = "Specialty not found")
    })
    public ResponseEntity<SmaileApiResponse<Void>> deleteSpeciality(
            @Parameter(description = "Specialty ID", required = true)
            @PathVariable final UUID id) {
        specialityService.delete(id);
        String message = i18nService.getMessage(MessageKey.SPECIALITY_DELETED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(null, message));
    }

}
