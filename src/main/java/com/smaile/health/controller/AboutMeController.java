package com.smaile.health.controller;

import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.model.response.UserResponse;
import com.smaile.health.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/about-me", produces = MediaType.APPLICATION_JSON_VALUE)
@AllArgsConstructor
@Tag(name = "About Me Controller")
public class AboutMeController {

    private final UserService userService;

    @GetMapping
    @Operation(summary = "Get the current user information")
    public ResponseEntity<SmaileApiResponse<UserResponse>> getAboutMe() {
        return ResponseEntity.ok(SmaileApiResponse.success(userService.getAboutMe()));
    }

}
