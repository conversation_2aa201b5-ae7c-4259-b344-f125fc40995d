package com.smaile.health.controller;

import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.Status;
import com.smaile.health.mapper.UserMapper;
import com.smaile.health.model.AuditLogDTO;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.UsersSummaryDTO;
import com.smaile.health.model.request.CreateUserRequest;
import com.smaile.health.model.request.UpdateUserRequest;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.model.response.UserResponse;
import com.smaile.health.repository.AuditLogRepository;
import com.smaile.health.service.AuditLogService;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/users", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "User Controller", description = "User management APIs")
public class UserController {

    private final UserService userService;
    private final UserMapper userMapper;
    private final AuditLogService auditLogService;

    @GetMapping("/{id}")
    @Operation(summary = "Get user info by id")
    @PreAuthorize("hasRole('USER') or hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('SMAILE-BE_USER')")
    public ResponseEntity<UserResponse> getUser(@PathVariable UUID id) {
        UserDTO userDto = userService.get(id);
        return ResponseEntity.ok(userMapper.toResponse(userDto));
    }

    @GetMapping("/query")
    @Operation(
            summary = "Find users by criteria. If organization_id is not provided, uses current user's organization and its children. SUPER_SMAILE_ADMIN users can query all users.")
    public ResponseEntity<PageResponse<UserResponse>> findUser(
            @RequestParam(required = false, value = "organization_id") UUID organizationId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) RoleEnum role,
            @RequestParam(required = false) Status status,
            @PageableDefault(sort = "lastUpdated", direction = Sort.Direction.DESC) Pageable pageable) {

        if (organizationId == null && !SecurityContextUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())) {
            organizationId = SecurityContextUtils.getOwnerOrganizationId();
        }

        Page<UserDTO> userDtoPage = userService.queryUserByCriteria(organizationId, keyword, role, status, pageable);
        return ResponseEntity.ok(new PageResponse<>(userDtoPage.map(userMapper::toResponse)));
    }

    @GetMapping("/summary")
    @Operation(summary = "Get system user summary statistic")
    public ResponseEntity<SmaileApiResponse<UsersSummaryDTO>> getUsersSummary() {
        return ResponseEntity.ok(SmaileApiResponse.success(userService.queryUsersSummaryData()));
    }

    @PostMapping
    @ApiResponse(responseCode = "201")
    @Operation(summary = "Create a new user")
    public ResponseEntity<BaseResponse<UUID>> createUser(@RequestBody @Valid final CreateUserRequest request) {
        UserDTO userDto = userMapper.toDTO(request);
        SmaileUserCredential userCredential = userService.createWithPasswordGenerated(request.getOrganizationId(),
                userDto, true, true);
        return new ResponseEntity<>(new BaseResponse<>(userCredential.getId()), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update user info")
    public ResponseEntity<BaseResponse<UUID>> updateUser(@PathVariable(name = "id") final UUID id,
                                                         @RequestBody @Valid final UpdateUserRequest request) {
        UserDTO userDto = userMapper.toDTO(request);
        userService.updateUser(id, userDto);
        return new ResponseEntity<>(new BaseResponse<>(id), HttpStatus.OK);
    }

    @PostMapping("/{id}/reset-password")
    @Operation(summary = "Trigger reset password flow for user")
    public ResponseEntity<SmaileApiResponse<UUID>> resetPassword(@PathVariable(name = "id") final UUID userId) {
        UUID orgId = null;
        UserDTO userDTO = userService.get(userId);
        if (!SecurityContextUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())) {
            orgId = userDTO.getOrganization().getId();
        }
        return ResponseEntity.ok(SmaileApiResponse.success(userService.resetPassword(orgId, userDTO)));
    }


    @PutMapping("/{id}/activate")
    @Operation(summary = "Activate a user")
    public ResponseEntity<SmaileApiResponse<String>> activateUser(@PathVariable(name = "id") final UUID userId) {
        UUID orgId = userService.getOrgIdByUserId(userId);
        userService.activate(orgId, userId);
        return ResponseEntity.ok(SmaileApiResponse.success("User activated successfully"));
    }

    @PutMapping("/{id}/deactivate")
    @Operation(summary = "Deactivate a user")
    public ResponseEntity<SmaileApiResponse<String>> deactivateUser(@PathVariable(name = "id") final UUID userId) {
        UUID orgId = userService.getOrgIdByUserId(userId);
        userService.deactivate(orgId, userId);
        return ResponseEntity.ok(SmaileApiResponse.success("User deactivated successfully"));
    }

    @GetMapping("/{id}/audit-logs")
    @Operation(summary = "Get all audit log related to user")
    public ResponseEntity<PageResponse<AuditLogDTO>> getAuditLog(
            @PathVariable("id") UUID userId,
            @PageableDefault(sort = "id", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        Page<AuditLogDTO> auditLogPage = auditLogService.findByEntityId(userId, pageable);
        return ResponseEntity.ok(new PageResponse<>(auditLogPage));
    }
}
