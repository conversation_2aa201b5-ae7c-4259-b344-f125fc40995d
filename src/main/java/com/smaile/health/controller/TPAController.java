package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.model.request.CreateTPARequestDTO;
import com.smaile.health.model.TPADTO;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.TPAService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/tpa", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Third Party Admin (TPA)", description = "Third Party Admin organization management APIs")
public class TPAController {

    private final TPAService tpaService;
    private final I18nService i18nService;

    @GetMapping("/query")
    @Operation(summary = "Search TPA organizations", description = "Search TPA organizations with pagination and filters. Returns TPAs with their related organizations filtered by user role: SUPER_SMAILE users (sysadmin) see all TPA types, IC users see only IC_TPA types, regular SMAILE users see only SMAILE_TPA types. Admin type filter searches for TPAs that have users with specific role codes. IC ID filter searches for TPAs that belong to a specific Insurance Company. User information is included for SUPER_SMAILE_ADMIN users.")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "TPA organizations retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Invalid parameters or validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<TPADTO>>> searchTPAs(
            @Parameter(description = "Market filter (e.g., 'US', 'EU', 'ASIA')", example = "US")
            @RequestParam(required = false) String market,
            @Parameter(description = "Status filter - ACTIVE or INACTIVE", example = "ACTIVE")
            @RequestParam(required = false) String status,
            @Parameter(description = "Name filter - partial match (case-insensitive)", example = "ABC TPA Services")
            @RequestParam(required = false) String name,
            @Parameter(description = "Registration number filter - partial match (case-insensitive)", example = "REG123456")
            @RequestParam(required = false) String registrationNumber,
            @Parameter(description = "Admin type filter - partial match on user role codes (case-insensitive)", example = "IC_TPA_ADMIN")
            @RequestParam(required = false) String adminType,
            @Parameter(description = "IC ID filter - filter TPAs by parent Insurance Company ID", example = "018f1234-5678-9abc-def0-123456789abc")
            @RequestParam(required = false) String icId,

            @Parameter(description = "Pagination parameters (page, size, sort)")
            @PageableDefault(size = 20, sort = "id") Pageable pageable) {

        PageResponse<TPADTO> pageResponse = PageResponse.of(tpaService.search(market, status, name, registrationNumber, adminType, icId, pageable));
        String message = i18nService.getMessage(MessageKey.TPA_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(pageResponse, message));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get TPA organization by ID", description = "Retrieve a specific TPA organization by its ID with related organizations. Access is restricted based on user role: SUPER_SMAILE users (sysadmin) can access all TPA types, IC users can only access IC_TPA types, regular SMAILE users can only access SMAILE_TPA types. User information is included for SUPER_SMAILE_ADMIN users.")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "TPA organization retrieved successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad request - Invalid ID format"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions or access denied to TPA type"),
            @ApiResponse(responseCode = "404", description = "Not found - TPA organization not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<TPADTO>> getTPA(
            @Parameter(description = "Unique identifier of the TPA organization", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {

        TPADTO tpa = tpaService.get(id);
        String message = i18nService.getMessage(MessageKey.TPA_RETRIEVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(tpa, message));
    }

    @PostMapping
    @Operation(summary = "Create TPA organization", description = "Create a new TPA organization. TPA type is automatically determined based on the current user's organization type (IC_TPA for IC users, SMAILE_TPA for SUPER_SMAILE users). Admin user information is required and the admin user will automatically receive an invitation email with temporary password.")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "TPA organization created successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions (IC or SUPER_SMAILE organization required)"),
            @ApiResponse(responseCode = "409", description = "Conflict - TPA organization with same name already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<UUID>> createTPA(
            @Parameter(
                    description = "TPA organization data to create",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = CreateTPARequestDTO.class)
                    )
            )
            @RequestBody @Valid final CreateTPARequestDTO requestDTO) {
        final UUID createdId = tpaService.create(requestDTO);
        String message = i18nService.getMessage(MessageKey.TPA_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(createdId, message));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update TPA organization", description = "Update an existing TPA organization")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "TPA organization updated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad request - Validation errors",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - TPA organization not found"),
            @ApiResponse(responseCode = "409", description = "Conflict - TPA organization with same name already exists"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<String>> updateTPA(
            @Parameter(description = "Unique identifier of the TPA organization to update", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id,
            @Parameter(
                    description = "Updated TPA organization data",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = TPADTO.class)
                    )
            )
            @RequestBody @Valid final TPADTO tpaDTO) {
        tpaService.update(id, tpaDTO);
        String message = i18nService.getMessage(MessageKey.TPA_UPDATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id.toString(), message));
    }

    @PutMapping("/{id}/activate")
    @Operation(summary = "Activate TPA organization", description = "Activate a TPA organization by setting status to ACTIVE")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "TPA organization activated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad request - Invalid ID format or already active"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - TPA organization not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<String>> activateTPA(
            @Parameter(description = "Unique identifier of the TPA organization to activate", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {
        tpaService.activate(id);
        String message = i18nService.getMessage(MessageKey.TPA_ACTIVATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id.toString(), message));
    }

    @PutMapping("/{id}/deactivate")
    @Operation(summary = "Deactivate TPA organization", description = "Deactivate a TPA organization by setting status to INACTIVE")
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "TPA organization deactivated successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad request - Invalid ID format or already inactive"),
            @ApiResponse(responseCode = "401", description = "Unauthorized - Authentication required"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Not found - TPA organization not found"),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SmaileApiResponse.class)
                    )
            )
    })
    public ResponseEntity<SmaileApiResponse<String>> deactivateTPA(
            @Parameter(description = "Unique identifier of the TPA organization to deactivate", example = "018f1234-5678-9abc-def0-123456789abc")
            @PathVariable(name = "id") final UUID id) {
        tpaService.deactivate(id);
        String message = i18nService.getMessage(MessageKey.TPA_DEACTIVATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(id.toString(), message));
    }


}
