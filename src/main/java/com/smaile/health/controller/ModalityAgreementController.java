package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.model.request.CreateModalityProcedureRequestDTO;
import com.smaile.health.model.request.LinkModalityAgreementRequest;
import com.smaile.health.model.request.ModalityProcedureSearchRequest;
import com.smaile.health.model.response.ModalityAgreementResponseDTO;
import com.smaile.health.model.response.ModalityProcedureResponseDTO;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityAgreementService;
import com.smaile.health.service.ModalityProcedureService;
import com.smaile.health.model.response.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping(value = "/modality-agreements", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Modality Agreement Controller", description = "Modality agreement management APIs")
public class ModalityAgreementController {

    private final ModalityAgreementService modalityAgreementService;

    private final I18nService i18nService;

    @PostMapping("/link")
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#request.modalityId)")
    public ResponseEntity<SmaileApiResponse<UUID>> link(
            @RequestBody @Valid final LinkModalityAgreementRequest request) {
        modalityAgreementService.linkAgreement(request.getModalityId(), request.getAgreementId());
        return ResponseEntity.ok(SmaileApiResponse.success(null, i18nService.getMessage(MessageKey.SUCCESS.getMessageKey())));
    }

    @PostMapping("/unlink")
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#request.modalityId)")
    public ResponseEntity<SmaileApiResponse<UUID>> deleteModalityProcedure(
            @RequestBody @Valid final LinkModalityAgreementRequest request
    ) {
        modalityAgreementService.unlinkAgreement(request.getModalityId(), request.getAgreementId());
        return ResponseEntity.ok(SmaileApiResponse.success(null, i18nService.getMessage(MessageKey.SUCCESS.getMessageKey())));
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('IC_ADMIN')")
    public ResponseEntity<SmaileApiResponse<PageResponse<ModalityAgreementResponseDTO>>> query(
            @RequestParam final UUID modalityId,
            @RequestParam(required = false) final String search,
            @PageableDefault(page = 0, size = 10, sort = "dateCreated", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        Page<ModalityAgreementResponseDTO> paged = modalityAgreementService.query(
                search, modalityId,
                pageable
        );
        return ResponseEntity.ok(SmaileApiResponse.success(PageResponse.of(paged), i18nService.getMessage(MessageKey.SUCCESS.getMessageKey())));
    }

}
