package com.smaile.health.controller;

import com.smaile.health.constants.InsuredStatusEnum;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.mapper.InsuredMapper;
import com.smaile.health.model.AuditLogDTO;
import com.smaile.health.model.InsuredDTO;
import com.smaile.health.model.request.CreateInsuredRequest;
import com.smaile.health.model.request.UpdateInsuredRequest;
import com.smaile.health.model.response.InsuredResponse;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.AuditLogService;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.InsuredService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.UUID;

@RestController
@RequestMapping("/insureds")
@Tag(name = "Insured", description = "Insured management APIs")
@RequiredArgsConstructor
public class InsuredController {
    private final InsuredMapper mapper;
    private final InsuredService insuredService;
    private final AuditLogService auditLogService;
    private final I18nService i18nService;

    @PreAuthorize(value = "hasRole('IC_ADMIN')")
    @Operation(summary = "Create Insured API")
    @PostMapping
    public ResponseEntity<SmaileApiResponse<UUID>> createInsured(@RequestBody @Valid CreateInsuredRequest request) {
        InsuredDTO insuredDTO = mapper.toDTO(request);
        insuredDTO.setIcOrgId(SecurityContextUtils.getOwnerOrganizationId());
        if (insuredDTO.getCoverageBeginDate().isAfter(insuredDTO.getCoverageEndDate())) {
            throw new SmaileRuntimeException(i18nService.getMessage("error.insured.coverage-range-invalid"));
        }
        return ResponseEntity.ok(SmaileApiResponse.success(insuredService.createInsured(insuredDTO)));
    }

    @Operation(summary = "Get Insured information by id")
    @GetMapping("/{id}")
    public ResponseEntity<SmaileApiResponse<InsuredResponse>> getById(@PathVariable("id") UUID id) {
        return ResponseEntity.ok(SmaileApiResponse.success(mapper.toResponse(insuredService.getById(id))));
    }

    @Operation(summary = "Query Insured list by criteria with pagination")
    @GetMapping("/query")
    public ResponseEntity<SmaileApiResponse<PageResponse<InsuredResponse>>> query(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "status", required = false) InsuredStatusEnum status,
            @RequestParam(value = "coverage_begin_date", required = false) LocalDate coverageBeginDate,
            @RequestParam(value = "coverage_end_date", required = false) LocalDate coverageEndDate,
            @PageableDefault(sort = "lastUpdated", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        Page<InsuredResponse> result = insuredService.query(keyword, status, coverageBeginDate, coverageEndDate, pageable);
        return ResponseEntity.ok(SmaileApiResponse.success(new PageResponse<>(result)));
    }

    @Operation(summary = "Update Insured information")
    @PutMapping("/{id}")
    public ResponseEntity<SmaileApiResponse<UUID>> updateInsured(@PathVariable("id") UUID id, @RequestBody @Valid UpdateInsuredRequest request) {
        InsuredDTO insuredDTO = mapper.toDTO(request);
        insuredDTO.setId(id);
        return ResponseEntity.ok(SmaileApiResponse.success(insuredService.updateInsured(insuredDTO)));
    }

    @Operation(summary = "Activate an Insured")
    @PutMapping("/{id}/activate")
    public ResponseEntity<SmaileApiResponse<UUID>> activeInsured(@PathVariable("id") UUID id) {
        return ResponseEntity.ok(SmaileApiResponse.success(insuredService.activeInsured(id)));
    }

    public record SuspendParam(@NotNull LocalDate effectiveDate, @NotNull String reason) {}
    @Operation(summary = "Suspend an Insured")
    @PutMapping("/{id}/suspend")
    public ResponseEntity<SmaileApiResponse<UUID>> suspendInsured(@PathVariable("id") UUID id, @RequestBody @Valid SuspendParam param) {
        return ResponseEntity.ok(SmaileApiResponse.success(insuredService.suspendInsured(id, param.effectiveDate(), param.reason())));
    }

    public record CancelParam(@NotNull LocalDate effectiveDate, @NotNull String reason) {}
    @Operation(summary = "Cancel an Insured")
    @PutMapping("/{id}/cancel")
    public ResponseEntity<SmaileApiResponse<UUID>> cancelInsured(@PathVariable("id") UUID id, @RequestBody @Valid CancelParam param) {
        return ResponseEntity.ok(SmaileApiResponse.success(insuredService.cancelInsured(id, param.effectiveDate(), param.reason())));
    }

    @GetMapping("/{id}/audit-logs")
    @Operation(summary = "Get Insured audit logs")
    public ResponseEntity<PageResponse<AuditLogDTO>> getAuditLogs(
            @PathVariable("id") UUID insuredId,
            @PageableDefault(sort = "id", direction = Sort.Direction.DESC) Pageable pageable
    ) {
        Page<AuditLogDTO> pageAuditLog = auditLogService.findByEntityId(insuredId, pageable);
        return ResponseEntity.ok(new PageResponse<>(pageAuditLog));
    }
}
