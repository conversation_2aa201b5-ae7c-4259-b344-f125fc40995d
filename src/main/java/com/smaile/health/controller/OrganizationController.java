package com.smaile.health.controller;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.model.FileUploadDTO;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.OrganizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/organizations", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Organization Controller", description = "Organization management APIs")
public class OrganizationController {

    private final OrganizationService organizationService;

    @GetMapping("/paged")
    @Operation(
            summary = "Get current organization with its child organizations. SUPER_SMAILE_ADMIN users can get all organizations.")
    @LogExecution
    public ResponseEntity<PageResponse<OrganizationDTO>> getAllOrganizationsPaged(
            @PageableDefault(size = 20, sort = "id") Pageable pageable) {

        if (SecurityContextUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())) {
            return ResponseEntity.ok(organizationService.getAllOrganizationsPaged(pageable));
        }

        UUID currentOrgId = SecurityContextUtils.getOwnerOrganizationId();
        return ResponseEntity.ok(organizationService.getCurrentOrgWithChildrenPaged(currentOrgId, pageable));
    }


    @PostMapping("/{id}/attachments")
    @Operation(summary = "Add attachment file")
    public ResponseEntity<SmaileApiResponse<UUID>> addAttachment(
            @PathVariable("id") UUID orgId,
            @RequestBody @Valid FileUploadDTO fileUploadDTO) {
        return ResponseEntity.ok(SmaileApiResponse.success(organizationService.addAttachment(orgId, fileUploadDTO)));
    }

    @DeleteMapping("/{id}/attachments/{file_id}")
    @Operation(summary = "Delete attachment file")
    public ResponseEntity<SmaileApiResponse<UUID>> deleteAttachment(
            @PathVariable("id") UUID orgId,
            @PathVariable("file_id") UUID attachmentId
    ) {
        return ResponseEntity.ok(SmaileApiResponse.success(organizationService.deleteAttachment(orgId, attachmentId)));
    }
}
