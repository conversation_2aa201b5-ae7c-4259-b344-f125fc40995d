package com.smaile.health.controller;

import com.smaile.health.service.UserService;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.UUID;

@RestController
@RequestMapping("/webhook")
@ApiResponse(description = "Webhook APIs for systems")
@Hidden
@RequiredArgsConstructor
@Slf4j
public class WebhookController {
    public record Event(@NotNull UUID userId, @NotNull EventType type, Long timestamp) {}

    enum EventType {
        LOGIN
    }
    private final UserService userService;

    @PostMapping("/iam")
    @Operation(summary = "webhook to handle event from IAM")
    public ResponseEntity<Void> handleIamWebhookEvent(@RequestBody @Valid Event param) {
        Long timestamp = param.timestamp();
        if (timestamp == null) timestamp = Instant.now().toEpochMilli();
        switch (param.type()) {
            case LOGIN -> userService.updateLastLogin(param.userId(), timestamp);
            default -> {
                log.warn("UNKNOWN webhook event: {}", param);
            }
        }
        return ResponseEntity.ok().build();
    }
}
