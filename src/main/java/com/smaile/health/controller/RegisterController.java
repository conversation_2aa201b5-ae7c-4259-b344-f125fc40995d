package com.smaile.health.controller;

import com.smaile.health.model.RegisterProfessionalFormDTO;
import com.smaile.health.model.RegisterProfessionalPresignedUrl;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.model.response.ListResponse;
import com.smaile.health.service.ProfessionalService;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.UUID;

@RestController
@RequestMapping(value = "/register", produces = MediaType.APPLICATION_JSON_VALUE)
@AllArgsConstructor
public class RegisterController {

    private final ProfessionalService professionalService;

    @PostMapping(value = "/professional")
    public ResponseEntity<BaseResponse<UUID>> registerProfessional(
            @RequestBody RegisterProfessionalFormDTO registerProfessionalFormDTO
    ) throws IOException {
        final UUID createdId = professionalService.create(registerProfessionalFormDTO);
        return ResponseEntity.ok(new BaseResponse<>(createdId));
    }

    @GetMapping(value = "/professional/presigned-url")
    public ResponseEntity<ListResponse<RegisterProfessionalPresignedUrl>> presignedUrl() throws IOException {
        return ResponseEntity.ok(new ListResponse<>(professionalService.createPreSignedUrl()));
    }

}
