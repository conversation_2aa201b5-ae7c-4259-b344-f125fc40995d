package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.model.request.CreateModalityProcedureRequestDTO;
import com.smaile.health.model.request.ModalityProcedureSearchRequest;
import com.smaile.health.model.response.ModalityProcedureResponseDTO;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityProcedureService;
import com.smaile.health.model.response.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/modality-procedures", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Modality Controller", description = "Modality management APIs")
public class ModalityProcedureController {

    private final ModalityProcedureService modalityProcedureService;

    private final I18nService i18nService;

    @PostMapping
    @Operation(summary = "Link procedure to a modality", description = "Link procedure to a modality")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Link procedure successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required"),
    })
    public ResponseEntity<SmaileApiResponse<UUID>> createModalityProcedure(
            @Parameter(
                    description = "procedure data to link",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = CreateModalityProcedureRequestDTO.class)
                    )
            )
            @RequestBody @Valid final CreateModalityProcedureRequestDTO requestDTO) {
        final UUID createdId = modalityProcedureService.create(requestDTO);
        String message = i18nService.getMessage(MessageKey.MODALITY_PROCEDURE_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(createdId, message));
    }

    @PostMapping("/{id}/unlink")
    @Operation(summary = "Remove a procedure from a modality", description = "Remove a procedure from a modality")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality procedure removed successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required"),
    })
    public ResponseEntity<SmaileApiResponse<UUID>> deleteModalityProcedure(
            @Parameter(description = "Modality Procedure ID", required = true)
            @PathVariable final UUID id) {
        modalityProcedureService.delete(id);
        String message = i18nService.getMessage(MessageKey.MODALITY_PROCEDURE_REMOVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(null, message));
    }

    @PostMapping("/search")
    @Operation(summary = "Get list modality procedure", description = "Get list modality procedure with search options")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required")
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<ModalityProcedureResponseDTO>>> query(
            @Schema(
                    description = "Text search by procedure name, code, description",
                    example = "Dental Basic",
                    maxLength = 255
            )
            @RequestBody ModalityProcedureSearchRequest requestData,
            @PageableDefault(page = 0, size = 10, sort = "dateCreated", direction = Sort.Direction.DESC) Pageable pageable
    ) {

        Page<ModalityProcedureResponseDTO> modalityProcedureDTOs = modalityProcedureService.query(
                requestData.getKeyword(),
                requestData.getModalityId(),
                pageable
        );

        String message = i18nService.getMessage(MessageKey.MODALITY_PROCEDURE_RETRIEVED.getKey());

        return ResponseEntity.ok(SmaileApiResponse.success(PageResponse.of(modalityProcedureDTOs), message));
    }

}
