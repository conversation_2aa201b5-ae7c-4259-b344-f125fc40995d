package com.smaile.health.controller;

import com.smaile.health.model.CountryDTO;
import com.smaile.health.model.MarketDTO;
import com.smaile.health.model.MarketSegmentDTO;
import com.smaile.health.model.ProofDTO;
import com.smaile.health.model.SpecialityDTO;
import com.smaile.health.model.response.GeneratePresignedUrlResponse;
import com.smaile.health.model.response.ListResponse;
import com.smaile.health.service.UtilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/utilities", produces = MediaType.APPLICATION_JSON_VALUE)
@AllArgsConstructor
@Tag(name = "Utility Controller", description = "Utility Controller APIs, this is public APIs")
public class UtilityController {

    private final UtilityService utilityService;

    @GetMapping("/markets")
    public ResponseEntity<ListResponse<MarketDTO>> getMarkets() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllMarket()));
    }

    @GetMapping("/countries")
    public ResponseEntity<ListResponse<CountryDTO>> getCountry() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllCountry()));
    }

    @GetMapping("/market-segments")
    public ResponseEntity<ListResponse<MarketSegmentDTO>> getMarketSegment() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllMarketSegment()));
    }

    @GetMapping("/professional-specialties")
    public ResponseEntity<ListResponse<SpecialityDTO>> getProfessionalSpecialties() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllProfessionalSpecialty()));
    }


    //TODO will be merged with professional-specialties into a single API for specialities
    @GetMapping("/authorized-specialties")
    public ResponseEntity<ListResponse<SpecialityDTO>> getAuthorizedSpecialties() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllAuthorizedSpecialties()));
    }

    @GetMapping("/proofs")
    public ResponseEntity<ListResponse<ProofDTO>> getProofs() {
        return ResponseEntity.ok(ListResponse.of(utilityService.getAllProofs()));
    }

    @Operation(summary = "Generate an url to upload file")
    @PostMapping("/generate-file-upload-url")
    public ResponseEntity<GeneratePresignedUrlResponse> generateUploadUrl() {
        return ResponseEntity.ok(utilityService.generatePresignedUrl());
    }

}
