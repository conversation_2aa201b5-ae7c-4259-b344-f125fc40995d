package com.smaile.health.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.constants.EntityType;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.AuditLogDTO;
import com.smaile.health.model.request.CreateAgreementDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.UpdateAgreementDTO;
import com.smaile.health.model.request.UpdateAgreementProcedureRequest;
import com.smaile.health.model.response.BaseResponse;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.service.AgreementService;
import com.smaile.health.service.AuditLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping(value = "/agreements", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Agreements Controller", description = "Agreements management APIs")
public class AgreementController {

    private final AgreementService agreementService;
    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;

    @GetMapping
    public ResponseEntity<PageResponse<AgreementDTO>> getPage(
            @Schema(
                    description = "Text search by long description, short description and contract id",
                    example = "CCNSxzz",
                    maxLength = 100
            )
            @RequestParam(required = false, name = "search") String search,
            @Schema(
                    description = "Filter query in JSON format. This api support fields: status, provider.id",
                    example = """
                            [
                              {
                                "field":"status",
                                "operator":"EQ",
                                "value":"ACTIVE"
                              }
                            ]
                            """,
                    maxLength = 100
            )
            @RequestParam(required = false, name = "filters") String filterQuery,
            @PageableDefault(size = 20, sort = "dateCreated", direction = Sort.Direction.DESC)
            Pageable pageable) throws JsonProcessingException {
        List<Filter> filters = new ArrayList<>();
        if (StringUtils.hasText(filterQuery)) {
            filters = objectMapper.readValue(filterQuery,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Filter.class));
        }
        return ResponseEntity.ok(agreementService.query(search, filters, pageable));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get details")
    //    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'agreements:*:read')")
    public ResponseEntity<BaseResponse<AgreementDTO>> detail(@PathVariable("id") UUID id) {
        return ResponseEntity.ok(BaseResponse.of(agreementService.detail(id)));
    }

    @PostMapping
    @Operation(summary = "Create Agreement")
    //    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #agreement.providerId, 'agreements:*:create')")
    public ResponseEntity<BaseResponse<UUID>> create(@RequestBody CreateAgreementDTO agreement) {
        return ResponseEntity.ok(BaseResponse.of(agreementService.create(agreement)));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update Agreement")
    //    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #agreement.providerId, 'agreements:*:update')")
    public ResponseEntity<Void> update(@PathVariable("id") UUID id,
                                       @RequestBody UpdateAgreementDTO agreement) {
        agreementService.update(id, agreement);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/procedures")
    @Operation(summary = "Update Agreement procedures")
    //    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #agreement.providerId, 'agreements:*:update')")
    public ResponseEntity<Void> updateProcedures(@PathVariable("id") UUID id,
                                                 @RequestBody UpdateAgreementProcedureRequest agreement) {
        agreementService.updateProcedure(id, agreement);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{id}/audit-logs")
    public ResponseEntity<PageResponse<AuditLogDTO>> getAuditLog(
            @PathVariable("id") UUID id,
            @Schema(
                    description = "Text search by description",
                    example = "CCNSxzz",
                    maxLength = 100
            )
            @RequestParam(required = false, name = "search") String search,
            @Schema(
                    description = "Filter query in JSON format. This api support fields: operation",
                    example = """
                            [
                              {
                                "field":"operation",
                                "operator":"EQ",
                                "value":"    INSERT | UPDATE | DELETE"
                              }
                            ]
                            """,
                    maxLength = 100
            )
            @RequestParam(required = false, name = "filters") String filterQuery,
            @PageableDefault(page = 0, size = 20, sort = "dateCreated", direction = Sort.Direction.DESC)
            Pageable pageable) throws JsonProcessingException {
        List<Filter> filters = new ArrayList<>();
        if (StringUtils.hasText(filterQuery)) {
            filters = objectMapper.readValue(filterQuery,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Filter.class));
        }
        return ResponseEntity.ok(
                PageResponse.of(auditLogService.search(search, EntityType.AGREEMENT.name(), id, filters, pageable)));
    }

}
