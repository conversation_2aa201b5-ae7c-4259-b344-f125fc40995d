package com.smaile.health.controller;

import com.smaile.health.constants.MessageKey;
import com.smaile.health.model.request.CreateModalityDiagnosisRequestDTO;
import com.smaile.health.model.request.ModalityDiagnosisSearchRequest;
import com.smaile.health.model.response.ModalityDiagnosisResponseDTO;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.model.response.SmaileApiResponse;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityDiagnosisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping(value = "/modality-diagnoses", produces = MediaType.APPLICATION_JSON_VALUE)
@RequiredArgsConstructor
@Tag(name = "Modality Diagnoses Controller", description = "Modality Diagnoses management APIs")
public class ModalityDiagnosisController {

    private final ModalityDiagnosisService modalityDiagnosisService;

    private final I18nService i18nService;

    @PostMapping
    @Operation(summary = "Link a diagnosis directly to a modality",
            description = "Link a diagnosis directly to a modality")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Diagnosis added successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required"),
    })
    public ResponseEntity<SmaileApiResponse<UUID>> createModality(
            @Parameter(
                    description = "Modality Diagnosis data to create",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = CreateModalityDiagnosisRequestDTO.class)
                    )
            )
            @RequestBody @Valid final CreateModalityDiagnosisRequestDTO requestDTO) {
        final UUID createdId = modalityDiagnosisService.create(requestDTO);
        String message = i18nService.getMessage(MessageKey.MODALITY_DIAGNOSIS_CREATED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(createdId, message));
    }

    @PostMapping("/{id}/unlink")
    @Operation(summary = "Remove an orphan diagnosis from a modality",
            description = "Remove an orphan diagnosis from a modality")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality diagnosis removed successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required"),
    })
    public ResponseEntity<SmaileApiResponse<UUID>> deleteModalityDiagnosis(
            @Parameter(description = "Modality Diagnosis ID", required = true)
            @PathVariable final UUID id) {
        modalityDiagnosisService.delete(id);
        String message = i18nService.getMessage(MessageKey.MODALITY_DIAGNOSIS_REMOVED.getKey());
        return ResponseEntity.ok(SmaileApiResponse.success(null, message));
    }

    @PostMapping("/search")
    @Operation(summary = "Get list modality diagnoses", description = "Get list modality diagnoses with search options")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Modality retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - IC_ADMIN role required")
    })
    public ResponseEntity<SmaileApiResponse<PageResponse<ModalityDiagnosisResponseDTO>>> query(
            @Schema(
                    description = "Text search by diagnosis, code, description",
                    example = "K01",
                    maxLength = 255
            )
            @RequestBody ModalityDiagnosisSearchRequest requestData,
            @PageableDefault(sort = "dateCreated",
                    direction = Sort.Direction.DESC) Pageable pageable
    ) {

        Page<ModalityDiagnosisResponseDTO> modalityProcedureDTOs = modalityDiagnosisService.query(
                requestData.getKeyword(),
                requestData.getModalityId(),
                pageable
        );

        String message = i18nService.getMessage(MessageKey.MODALITY_PROCEDURE_RETRIEVED.getKey());

        return ResponseEntity.ok(SmaileApiResponse.success(PageResponse.of(modalityProcedureDTOs), message));
    }

}

