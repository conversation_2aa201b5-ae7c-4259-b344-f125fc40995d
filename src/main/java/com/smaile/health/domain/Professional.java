package com.smaile.health.domain;

import com.smaile.health.model.ProfessionalLicenseDTO;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.util.List;

@Entity
@Table(name = "professionals")
@PrimaryKeyJoinColumn(name = "id")
@Getter
@Setter
public class Professional extends Organization {

    @Column(name = "full_professional_name", nullable = false)
    private String fullProfessionalName;

    @Column(name = "primary_license_id")
    private String primaryLicenseId;

    @Column(name = "primary_practice_market")
    private String primaryPracticeMarket;

    @Column(name = "country")
    private String country;

    @Column(name = "market_segment")
    private String marketSegment;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "username")
    private String username;

    @Type(JsonType.class)
    @Column(name = "licenses")
    private List<ProfessionalLicenseDTO> licenses;

    @ManyToMany(fetch = FetchType.EAGER, cascade = CascadeType.PERSIST)
    @JoinTable(
            name = "professional_speciality",
            joinColumns = @JoinColumn(name = "professional_id"),
            inverseJoinColumns = @JoinColumn(name = "speciality_id")
    )
    private List<Speciality> specialities;

    @OneToMany(cascade = CascadeType.REMOVE, mappedBy = "professional", fetch = FetchType.EAGER)
    private List<ProfessionalLob> professionalLobs;

}
