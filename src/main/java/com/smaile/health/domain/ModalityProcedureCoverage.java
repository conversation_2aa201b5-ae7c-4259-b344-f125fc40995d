package com.smaile.health.domain;

import com.smaile.health.constants.NetworkType;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "modality_procedure_coverage")
public class ModalityProcedureCoverage extends BaseEntity {
    @Id
    @Column(name = "id", nullable = false)
    private UUID id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "modality_procedure_id", nullable = false)
    private ModalityProcedure modalityProcedure;

    @NotNull
    @Column(name = "network_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private NetworkType networkType;

    @Column(name = "fixed_amount", precision = 18, scale = 2)
    private BigDecimal fixedAmount;

    @Column(name = "percentage")
    private Integer percentage;

    @Column(name = "deductible_amount", precision = 18, scale = 2)
    private BigDecimal deductibleAmount;

    public ModalityProcedureCoverage configureWithIdAndProcedure(ModalityProcedure modalityProcedure) {
        this.setId(UUIDv7.generate());
        this.modalityProcedure = modalityProcedure;

        return this;
    }
}