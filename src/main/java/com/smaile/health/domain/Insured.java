package com.smaile.health.domain;

import com.smaile.health.audit.AuditableEntity;
import com.smaile.health.audit.AuditableField;
import com.smaile.health.constants.EntityType;
import com.smaile.health.constants.InsuredGenderEnum;
import com.smaile.health.constants.InsuredStatusEnum;
import com.smaile.health.constants.LegalIdTypeEnum;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@AuditableEntity(entityName = "Insured", entityEnum = EntityType.INSURED)
@Table(name = "insureds")
public class Insured extends BaseEntity {
    @Id
    @Column(name = "id", nullable = false)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "modality_id")
    private Modality modality;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ic_org_id")
    private InsuranceCompany icOrg;

    @AuditableField(displayName = "Insured Code")
    @Size(max = 255)
    @NotNull
    @Column(name = "insured_code", nullable = false)
    private String insuredCode;

    @AuditableField(displayName = "First Effective Date")
    @Column(name = "first_effective_date")
    private LocalDate firstEffectiveDate;

    @AuditableField(displayName = "Coverage Begin Date")
    @NotNull
    @Column(name = "coverage_begin_date", nullable = false)
    private LocalDate coverageBeginDate;

    @AuditableField(displayName = "Coverage End Date")
    @NotNull
    @Column(name = "coverage_end_date", nullable = false)
    private LocalDate coverageEndDate;

    @AuditableField(displayName = "Status")
    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "status", nullable = false, length = 100)
    private InsuredStatusEnum status;

    @AuditableField(displayName = "Status Effective Date")
    @Column(name = "status_date")
    private LocalDate statusDate;

    @AuditableField(displayName = "Status Reason")
    @Column(name = "status_reason")
    private String statusReason;

    @AuditableField(displayName = "Legal Id Type")
    @Enumerated(EnumType.STRING)
    @Column(name = "legal_id_type", length = 100)
    private LegalIdTypeEnum legalIdType;

    @AuditableField(displayName = "Legal Id Value")
    @Size(max = 100)
    @Column(name = "legal_id_value", length = 100)
    private String legalIdValue;

    @AuditableField(displayName = "Gender")
    @Enumerated(EnumType.STRING)
    @Column(name = "gender")
    private InsuredGenderEnum gender;

    @AuditableField(displayName = "Date Of Birth")
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @AuditableField(displayName = "First Name")
    @Size(max = 255)
    @Column(name = "first_name")
    private String firstName;

    @AuditableField(displayName = "Last Name")
    @Size(max = 255)
    @Column(name = "last_name")
    private String lastName;

    @AuditableField(displayName = "Is Deleted")
    @NotNull
    @ColumnDefault("false")
    @Column(name = "is_deleted")
    private boolean isDeleted;
}