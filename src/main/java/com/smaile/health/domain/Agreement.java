package com.smaile.health.domain;

import com.smaile.health.audit.AuditableEntity;
import com.smaile.health.audit.AuditableField;
import com.smaile.health.constants.EntityType;
import com.smaile.health.constants.Status;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Entity
@Table(name = "agreements")
@Getter
@Setter
@AuditableEntity(entityName = "Agreement", entityEnum = EntityType.AGREEMENT)
public class Agreement extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(nullable = false, unique = true)
    @AuditableField(displayName = "Contract ID")
    private String contractId;

    @Column(nullable = false)
    @AuditableField(displayName = "Short Description")
    private String shortDescription;

    @Column(columnDefinition = "TEXT")
    @AuditableField(displayName = "Long Description")
    private String longDescription;

    @Column
    private Instant effectiveTime;

    @Column
    private Instant cancellationTime;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @AuditableField(displayName = "Status")
    private Status status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "medical_provider_id", nullable = false)
    private MedicalProvider provider;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "agreement", cascade = CascadeType.ALL, orphanRemoval = true)
    @AuditableField(displayName = "Procedures Details")
    private List<AgreementProceduresDetail> proceduresDetails = new ArrayList<>();

    @OneToMany(mappedBy = "agreement")
    @AuditableField(displayName = "IC Agreement")
    private List<IcAgreement> icAgreements;

    public static class Fields {

        public static final String ID = "id";
        public static final String CONTRACT_ID = "contractId";
        public static final String SORT_DESCRIPTION = "shortDescription";
        public static final String LONG_DESCRIPTION = "longDescription";
        public static final String EFFECTIVE_TIME = "effectiveTime";
        public static final String CANCELLATION_TIME = "cancellationTime";
        public static final String STATUS = "status";
        public static final String PROVIDER = "provider";
        public static final String PROVIDER_ID = "provider.id";
        public static final String PROCEDURES_DETAILS = "proceduresDetails";

    }

    /**
     * Helper method to add a procedure detail to the agreement
     *
     * @param procedure The procedure detail to add
     */
    public AgreementProceduresDetail addProcedureDetail(Procedure procedure, BigDecimal cost) {
        Optional<AgreementProceduresDetail> existingOpt = proceduresDetails.stream()
                .filter(ap -> ap.getProcedure().equals(procedure))
                .findFirst();

        if (existingOpt.isPresent()) {
            AgreementProceduresDetail existing = existingOpt.get();
            existing.setCost(cost);
            return existing;
        }

        AgreementProceduresDetail agreementProceduresDetail = new AgreementProceduresDetail(this, procedure, cost);
        proceduresDetails.add(agreementProceduresDetail);
        return agreementProceduresDetail;
    }

    /**
     * Helper method to remove all the procedure detail from the agreement
     */
    public boolean removeProcedureDetails() {
        proceduresDetails.clear();
        return true;
    }

    /**
     * Helper method to remove a procedure detail from the agreement
     *
     * @param proceduresDetail The procedure detail to remove
     */
    public void removeProcedureDetail(AgreementProceduresDetail proceduresDetail) {
        proceduresDetails.remove(proceduresDetail);
    }

    /**
     * Helper method to update the procedures details of the agreement
     *
     * @param updateProcedureDetails The new procedures details to set
     */
    public void updateProceduresDetails(Map<Procedure, BigDecimal> updateProcedureDetails) {
        proceduresDetails.clear();

        if (updateProcedureDetails != null && !updateProcedureDetails.isEmpty()) {
            for (Map.Entry<Procedure, BigDecimal> entry : updateProcedureDetails.entrySet()) {
                Procedure procedure = entry.getKey();
                BigDecimal cost = entry.getValue();

                AgreementProceduresDetail newDetail = new AgreementProceduresDetail(this, procedure, cost);
                proceduresDetails.add(newDetail);
            }
        }
    }

}
