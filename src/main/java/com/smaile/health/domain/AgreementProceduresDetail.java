package com.smaile.health.domain;

import com.smaile.health.audit.AuditableEntity;
import com.smaile.health.audit.AuditableField;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import com.smaile.health.domain.id.AgreementProceduresDetailId;
import jakarta.persistence.*;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

import java.util.Objects;
import java.util.UUID;


@Entity
@Table(name = "agreement_procedure_details")
@Getter
@Setter
//@AuditableEntity(entityName = "AgreementProceduresDetail")
public class AgreementProceduresDetail extends BaseEntity {

    @EmbeddedId
    private AgreementProceduresDetailId id;

    @ManyToOne
    @JoinColumn(name = "agreement_id", nullable = false)
    @MapsId("agreementId")
    private Agreement agreement;

    @ManyToOne
    @JoinColumn(name = "procedure_id", nullable = false)
    @MapsId("procedureId")
    private Procedure procedure;

    @Column(nullable = false)
    @AuditableField(displayName = "Cost")
    private BigDecimal cost;

    public AgreementProceduresDetail() {
    }

    public AgreementProceduresDetail(Agreement agreement, Procedure procedure, BigDecimal cost) {
        if (agreement == null || procedure == null) {
            throw new IllegalArgumentException("Agreement and Procedure cannot be null");
        }
        if (agreement.getId() == null || procedure.getId() == null) {
            throw new IllegalArgumentException("Agreement ID and Procedure ID cannot be null");
        }

        this.agreement = agreement;
        this.procedure = procedure;
        this.cost = cost;
        this.id = new AgreementProceduresDetailId(agreement.getId(), procedure.getId());
    }

    /**
     * Helper method to get the agreement ID
     *
     * @return The agreement ID
     */
    public UUID getAgreementId() {
        return id != null ? id.getAgreementId() : null;
    }

    /**
     * Helper method to get the procedure ID
     *
     * @return The procedure ID
     */
    public UUID getProcedureId() {
        return id != null ? id.getProcedureId() : null;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof AgreementProceduresDetail that)) {
            return false;
        }
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

}
