package com.smaile.health.domain;

import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.Frequency;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "modality_procedures")
public class ModalityProcedure extends BaseEntity {
    @Id
    @Column(name = "id", nullable = false)
    private UUID id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "modality_id", nullable = false)
    private Modality modality;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "procedure_id", nullable = false)
    private Procedure procedure;

    @Column(name = "procedure_code", nullable = false)
    private String procedureCode; // ADA CDT code or custom country code

    @Column(name = "override_frequency", length = 10)
    @Enumerated(EnumType.STRING)
    private Frequency overrideFrequency;

    @Column(name = "override_market_cost_amount", precision = 18, scale = 2)
    private BigDecimal overrideMarketCostAmount;

    @Column(name = "age_group", length = 100)
    @Enumerated(EnumType.STRING)
    private AgeGroup ageGroup;

    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;

    @Column(name = "cancellation_date")
    private LocalDate cancellationDate;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "modality_procedure_diagnosis",
            joinColumns = @JoinColumn(name = "modality_procedure_id"),
            inverseJoinColumns = @JoinColumn(name = "modality_diagnosis_id")
    )
    private Set<ModalityDiagnosis> modalityDiagnoses = new HashSet<>();


    @OneToMany(mappedBy = "modalityProcedure")
    private Set<ModalityProcedureCoverage> coverages = new HashSet<>();

    public ModalityProcedure configureWithIdAnd(Modality modality, Procedure procedure) {
        this.setId(UUIDv7.generate());
        this.setModality(modality);
        this.setProcedure(procedure);

        return this;
    }

    public ModalityProcedure addModalityDiagnosis(ModalityDiagnosis diagnosis) {
        this.modalityDiagnoses.add(diagnosis);

        return this;
    }
}