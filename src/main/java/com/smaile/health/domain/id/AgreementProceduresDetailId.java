package com.smaile.health.domain.id;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AgreementProceduresDetailId implements Serializable {

    @Column(name = "agreement_id")
    private UUID agreementId;

    @Column(name = "procedure_id")
    private UUID procedureId;

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof AgreementProceduresDetailId that)) {
            return false;
        }
        return Objects.equals(agreementId, that.agreementId) && Objects.equals(procedureId,
                that.procedureId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(agreementId, procedureId);
    }

}
