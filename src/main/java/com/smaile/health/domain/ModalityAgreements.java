package com.smaile.health.domain;

import com.smaile.health.domain.id.ModalityAgreementId;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "modality_agreements")
public class ModalityAgreements extends BaseEntity {

    @EmbeddedId
    private ModalityAgreementId id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "modality_id", nullable = false)
    @MapsId("modalityId")
    private Modality modality;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "agreement_id", nullable = false)
    @MapsId("agreementId")
    private Agreement agreement;

    public static class Fields {
        public static final String AGREEMENT = "agreement";
        public static final String MODALITY = "modality";
    }
}