package com.smaile.health.domain;

import com.smaile.health.constants.AuditOperation;
import com.smaile.health.constants.EntityType;
import io.hypersistence.utils.hibernate.type.array.ListArrayType;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Audit log entity for tracking changes to business entities.
 * Uses JSON fields for efficient storage of changed values.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/09/07
 */
@Entity
@Table(name = "audit_log")
@Data
@NoArgsConstructor
public class AuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "entity_name", nullable = false, length = 100)
    @Enumerated(EnumType.STRING)
    private EntityType entityName;

    @Column(name = "entity_id", nullable = false, length = 100)
    private String entityId;

    @Column(name = "operation", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private AuditOperation operation;

    @Column(name = "old_values", columnDefinition = "JSONB")
    @Type(JsonType.class)
    private String oldValues;

    @Column(name = "new_values", columnDefinition = "JSONB")
    @Type(JsonType.class)
    private String newValues;

    @Column(name = "changed_columns", columnDefinition = "TEXT[]")
    @Type(ListArrayType.class)
    private List<String> changedColumns;

    @Column(name = "created_by", nullable = false, length = 100)
    private String createdBy;

    public static class Fields {

        public static final String ID = "id";
        public static final String ENTITY_NAME = "entityName";
        public static final String ENTITY_ID = "entityId";
        public static final String OPERATION = "operation";

    }
}
