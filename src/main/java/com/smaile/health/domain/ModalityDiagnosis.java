package com.smaile.health.domain;

import com.smaile.health.constants.Frequency;
import com.smaile.health.constants.ModalityDiagnosisType;
import com.smaile.health.constants.NetworkType;
import com.smaile.health.model.request.CreateModalityProcedureRequestDTO;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "modality_diagnosis")
public class ModalityDiagnosis extends BaseEntity{
    @Id
    @Column(name = "id", nullable = false)
    private UUID id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "modality_id", nullable = false)
    private Modality modality;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "diagnosis_id", nullable = false)
    private Diagnosis diagnosis;


    @Column(name = "diagnosis_code", nullable = false)
    private String diagnosisCode; // e.g., ICD-10 codes

    @NotNull
    @Column(name = "network_type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private NetworkType networkType;

    @Column(name = "override_frequency", length = 10)
    @Enumerated(EnumType.STRING)
    private Frequency overrideFrequency;

    @Column(name = "override_market_cost_amount", precision = 18, scale = 2)
    private BigDecimal overrideMarketCostAmount;

    @NotNull
    @Column(name = "type", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private ModalityDiagnosisType type;

    @Column(name = "effective_date", nullable = false)
    private LocalDate effectiveDate;

    @Column(name = "cancellation_date")
    private LocalDate cancellationDate;

    @ManyToMany(mappedBy = "modalityDiagnoses")
    private List<ModalityProcedure> modalityProcedures = new ArrayList<>();

    public static ModalityDiagnosis fromProcedureDto(Modality modality, Diagnosis diagnosis, CreateModalityProcedureRequestDTO requestDTO) {
        ModalityDiagnosis modalityDiagnosis = new ModalityDiagnosis();

        modalityDiagnosis.setId(UUIDv7.generate());
        modalityDiagnosis.setModality(modality);
        modalityDiagnosis.setDiagnosis(diagnosis);
        modalityDiagnosis.setDiagnosisCode(diagnosis.getCode());
        modalityDiagnosis.setNetworkType(modality.getNetworkType());
        modalityDiagnosis.setOverrideFrequency(requestDTO.getOverrideFrequency());
        modalityDiagnosis.setOverrideMarketCostAmount(requestDTO.getOverrideMarketCostAmount());
        modalityDiagnosis.setType(ModalityDiagnosisType.LINKED);
        modalityDiagnosis.setEffectiveDate(requestDTO.getEffectiveDate());
        modalityDiagnosis.setCancellationDate(requestDTO.getCancellationDate());

        return modalityDiagnosis;
    }

    public ModalityDiagnosis configureAsOrphan(Diagnosis diagnosis, Modality modality) {
        this.setId(UUIDv7.generate());
        this.setDiagnosis(diagnosis);
        this.setModality(modality);
        this.setType(ModalityDiagnosisType.ORPHANED);

        return this;
    }
}