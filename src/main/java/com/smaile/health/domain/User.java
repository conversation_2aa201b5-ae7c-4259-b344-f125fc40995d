package com.smaile.health.domain;

import com.smaile.health.audit.AuditableEntity;
import com.smaile.health.audit.AuditableField;
import com.smaile.health.constants.EntityType;
import com.smaile.health.constants.UserStatus;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "users")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@AuditableEntity(entityName = "User", entityEnum = EntityType.USER)
public class User extends BaseEntity {

    @Id
    @Column(nullable = false, updatable = false)
    private UUID id;

    @Column(name = "keycloak_id", nullable = false, unique = true)
    private String keycloakId;

    @AuditableField(displayName = "Email")
    @Column(name = "email", nullable = false, unique = true)
    private String email;

    @AuditableField(displayName = "Username")
    @Column(name = "username", nullable = false, unique = true)
    private String username;

    @AuditableField(displayName = "Full name")
    @Column(name = "full_name", nullable = false)
    private String fullName;

    @AuditableField(displayName = "Phone")
    @Column(name = "phone", length = 50)
    private String phone;

    @AuditableField(displayName = "Status")
    @Column(name = "status", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private UserStatus status;

    @Column
    private OffsetDateTime lastLogin;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id")
    private Organization organization;

    @OneToMany(mappedBy = "user", fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
    private List<UserOrganization> userOrganizations;

    // The helper method to set status from string
    public void setStatusText(String status) {
        this.status = UserStatus.resolve(status);
    }

}
