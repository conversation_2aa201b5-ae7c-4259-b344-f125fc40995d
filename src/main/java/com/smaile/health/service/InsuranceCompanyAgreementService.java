package com.smaile.health.service;

import com.smaile.health.model.InsuranceCompanyAgreementDTO;
import com.smaile.health.model.request.CreateInsuranceCompanyAgreementRequest;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.UpdateInsuranceCompanyAgreementRequest;
import com.smaile.health.model.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface InsuranceCompanyAgreementService {

    PageResponse<InsuranceCompanyAgreementDTO> query(String search, List<Filter> filters, Pageable pageable);

    InsuranceCompanyAgreementDTO detail(UUID agreementId);

    InsuranceCompanyAgreementDTO create(CreateInsuranceCompanyAgreementRequest icAgreement);

    InsuranceCompanyAgreementDTO update(UpdateInsuranceCompanyAgreementRequest icAgreement);

}
