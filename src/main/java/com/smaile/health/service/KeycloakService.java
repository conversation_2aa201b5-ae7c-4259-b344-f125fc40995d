package com.smaile.health.service;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.config.IamConfig;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.model.UserDTO;
import jakarta.annotation.PostConstruct;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.OAuth2Constants;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class KeycloakService implements IdentityProvider {

    private final IamConfig iamConfig;
    private UsersResource usersResource;

    @PostConstruct
    void postConstruct() {
        try (Keycloak keycloak = KeycloakBuilder.builder()
                .serverUrl(iamConfig.getEndpoint())
                .realm(iamConfig.getRealm())
                .clientId(iamConfig.getClientId())
                .clientSecret(iamConfig.getClientSecret())
                .grantType(OAuth2Constants.CLIENT_CREDENTIALS)
                .build()) {
            usersResource = keycloak.realm(iamConfig.getRealm()).users();
            log.debug("Keycloak user resource initialized");
        } catch (Exception e) {
            log.error("Failed to initialize Keycloak: {}", e.getMessage());
            throw new SmaileRuntimeException("Failed to initialize Keycloak: %s".formatted(e.getMessage()));
        }
    }

    @LogExecution
    @Override
    public String createUser(UserDTO userDto) {
        validateUserDoesNotExist(userDto.getUsername(), userDto.getEmail());

        UserRepresentation kcUser = createBasicUserRepresentation(userDto.getEmail(), userDto.getUsername());
        return createUserInKeycloak(kcUser);
    }

    @LogExecution
    @Override
    public String createUserWithPassword(UserDTO userDto, String password, boolean hasTempPassword) {
        validateUserDoesNotExist(userDto.getUsername(), userDto.getEmail());

        UserRepresentation kcUser = createBasicUserRepresentation(userDto.getEmail(), userDto.getUsername());
        kcUser.setEmailVerified(true);

        kcUser.setCredentials(List.of(createPasswordCredential(password, hasTempPassword)));
        if (hasTempPassword) {
            kcUser.setRequiredActions(List.of("UPDATE_PASSWORD"));
        }

        return createUserInKeycloak(kcUser);
    }

    @LogExecution
    @Override
    public Optional<UUID> findByEmailAndUsername(String email, String username) {
        List<UserRepresentation> userList = usersResource.searchByEmail(email, true);
        if (!userList.isEmpty()) {
            return Optional.of(UUID.fromString(userList.get(0).getId()));
        }
        userList = usersResource.searchByUsername(username, true);
        if (!userList.isEmpty()) {
            return Optional.of(UUID.fromString(userList.get(0).getId()));
        }
        return Optional.empty();
    }

    @Override
    @LogExecution
    public void resetPassword(String userKeycloakId, String newPassword) {
        try {
            UserResource user = usersResource.get(userKeycloakId);
            CredentialRepresentation credential = createPasswordCredential(newPassword, true);
            user.resetPassword(credential);
        } catch (Exception e) {
            throw new SmaileRuntimeException(
                    "Cannot find user id = [%s] on external provider".formatted(userKeycloakId));
        }
    }

    @LogExecution
    @Override
    public void enableUser(String userKeycloakId) {
        try {
            UserResource user = usersResource.get(userKeycloakId);
            if (user == null || user.toRepresentation().isEnabled()) {
                return;
            }
            UserRepresentation userRep = user.toRepresentation();
            userRep.setEnabled(true);
            user.update(userRep);
            log.debug("Enabled user in Keycloak with ID: {}", userKeycloakId);
        } catch (Exception e) {
            log.error("Failed to enable user in Keycloak with ID: {}", userKeycloakId, e);
            throw new SmaileRuntimeException(
                    "Cannot enable user id = [%s] on external provider".formatted(userKeycloakId));
        }
    }

    @LogExecution
    @Override
    public void disableUser(String userKeycloakId) {
        try {
            UserResource user = usersResource.get(userKeycloakId);
            if (user == null || !user.toRepresentation().isEnabled()) {
                return;
            }
            UserRepresentation userRep = user.toRepresentation();
            userRep.setEnabled(false);
            user.update(userRep);
            log.debug("Disabled user in Keycloak with ID: {}", userKeycloakId);
        } catch (Exception e) {
            log.error("Failed to disable user in Keycloak with ID: {}", userKeycloakId, e);
            throw new SmaileRuntimeException(
                    "Cannot disable user id = [%s] on external provider".formatted(userKeycloakId));
        }
    }

    @LogExecution
    private String createUserInKeycloak(UserRepresentation kcUser) {
        log.debug(
                "Creating user in Keycloak with data: username={}, email={}, enabled={}, emailVerified={}, requiredActions={}",
                kcUser.getUsername(), kcUser.getEmail(), kcUser.isEnabled(),
                kcUser.isEmailVerified(), kcUser.getRequiredActions());

        Response response = usersResource.create(kcUser);
        log.debug("Keycloak response headers: {}", response.getHeaders().toString());
        int responseStatus = response.getStatus();

        if (responseStatus == HttpStatus.CONFLICT.value()) {
            throw new SmaileRuntimeException(
                    "User with email = %s and/or %s existed".formatted(kcUser.getEmail(), kcUser.getUsername()));
        } else if (responseStatus != HttpStatus.CREATED.value()) {
            // Read the error response body for details
            String errorBody = response.readEntity(String.class);
            log.error("Keycloak error response body: {}", errorBody);

            String errorMessage = "Failed to create user, external Identity Provider responded status: %s".formatted(
                    responseStatus);
            throw new SmaileRuntimeException(errorMessage);
        }

        String createdUserLocation = String.valueOf(response.getHeaders().get("Location"));
        response.close();
        return createdUserLocation.substring(createdUserLocation.lastIndexOf('/') + 1,
                createdUserLocation.length() - 1);
    }

    private void validateUserDoesNotExist(String username, String email) {
        if (!usersResource.searchByUsername(username, true).isEmpty()) {
            throw new SmaileRuntimeException("User with username [%s] existed".formatted(username));
        }
        if (!usersResource.searchByEmail(email, true).isEmpty()) {
            throw new SmaileRuntimeException("User with email [%s] existed".formatted(email));
        }
    }

    private UserRepresentation createBasicUserRepresentation(String email, String username) {
        UserRepresentation kcUser = new UserRepresentation();
        kcUser.setEmail(email);
        kcUser.setUsername(username);
        kcUser.setEnabled(true);
        return kcUser;
    }

    private CredentialRepresentation createPasswordCredential(String password, boolean temporary) {
        CredentialRepresentation passwordCredential = new CredentialRepresentation();
        passwordCredential.setValue(password);
        passwordCredential.setType(CredentialRepresentation.PASSWORD);
        passwordCredential.setTemporary(temporary);
        return passwordCredential;
    }

}
