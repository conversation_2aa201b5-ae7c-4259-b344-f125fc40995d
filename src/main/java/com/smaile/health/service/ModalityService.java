package com.smaile.health.service;

import com.smaile.health.model.ModalitySearchCriteria;
import com.smaile.health.model.request.CreateModalityRequestDTO;
import com.smaile.health.model.request.UpdateModalityRequestDTO;
import com.smaile.health.model.response.DetailModalityResponse;
import com.smaile.health.model.response.ModalitySummaryResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface ModalityService {
    UUID create(CreateModalityRequestDTO createModalityRequestDTO);

    void update(UUID modalityId, UpdateModalityRequestDTO updateModalityRequestDTO);

    DetailModalityResponse detail(UUID modalityId);

    Page<ModalitySummaryResponse> query(ModalitySearchCriteria criteria, Pageable pageable);
}
