package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.mapper.AgreementMapper;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.mapper.ProfessionalMapper;
import com.smaile.health.model.FileUploadDTO;
import com.smaile.health.model.MedicalProviderDTO;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.repository.AgreementRepository;
import com.smaile.health.repository.MedicalProviderRepository;
import com.smaile.health.repository.OrgAttachmentRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.ProfessionalRepository;
import com.smaile.health.repository.specification.MedicalProviderSpecification;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.MedicalProviderService;
import com.smaile.health.service.ObjectStorageService;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.UUIDv7;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class MedicalProviderServiceImpl implements MedicalProviderService {

    private final MedicalProviderRepository medicalProviderRepository;
    private final AgreementRepository agreementRepository;
    private final OrganizationRepository organizationRepository;
    private final ProfessionalRepository professionalRepository;

    private final MedicalProviderMapper medicalProviderMapper;
    private final AgreementMapper agreementMapper;
    private final ProfessionalMapper professionalMapper;

    private final UserService userService;
    private final EmailService emailService;
    private final OrgAttachmentRepository orgAttachmentRepository;
    private final ObjectStorageService storageService;
    private final OrganizationService organizationService;

    @Override
    @LogExecution
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #scopedOrgId, 'organizations:*:create')")
    @Transactional
    public UUID create(UUID scopedOrgId, MedicalProviderDTO dto, UserDTO adminDto) {
        Organization parentOrg = organizationRepository.findById(scopedOrgId)
                .orElseThrow(() -> new SmaileRuntimeException("Org id %s not found".formatted(scopedOrgId)));

        validateMedicalProviderNameNotExist(dto);

        if (!userService.isAvailableToCreate(adminDto)) {
            throw new SmaileRuntimeException(
                    "Admin user email = %s and/or username = %s] existed".formatted(adminDto.getEmail(),
                            adminDto.getUsername()));
        }

        // Create medical provider
        MedicalProvider medicalProviderEntity = medicalProviderMapper.toEntity(dto);
        medicalProviderEntity.setId(UUIDv7.generate());
        medicalProviderEntity.setParent(parentOrg);
        medicalProviderEntity.setType(OrganizationType.valueOf(parentOrg.getType().getCode() + "_MP"));
        medicalProviderRepository.save(medicalProviderEntity);

        if (CollectionUtils.isNotEmpty(dto.getAttachmentFiles())) {
            organizationService.saveAttachmentFiles(medicalProviderEntity, dto.getAttachmentFiles());
        }

        String roleCode = Objects.equals(parentOrg.getType(), OrganizationType.SUPER_SMAILE) ?
                "SMAILE_MP_ADMIN" : (parentOrg.getType().name() + "_MP_ADMIN");

        adminDto.setOrganizationId(medicalProviderEntity.getId());
        adminDto.setRoleCode(roleCode);

        SmaileUserCredential createdCredential = userService.createWithPasswordGenerated(medicalProviderEntity.getId(),
                adminDto, false, true);

        emailService.sendOrganizationOnboardNotification(adminDto, medicalProviderEntity, createdCredential);
        return medicalProviderEntity.getId();
    }

    @Override
    @LogExecution
    public MedicalProviderDTO getById(UUID id) {
        MedicalProvider medicalProvider = medicalProviderRepository.findOne(
                (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(cb.equal(root.get("id"), id));
                    predicates.add(cb.isFalse(root.get("isDeleted")));
                    return predicates.stream().reduce(cb::and).orElseGet(cb::conjunction);
                }
        ).orElseThrow(() -> new SmaileRuntimeException("Medical Provider with id = %s not found".formatted(id)));
        MedicalProviderDTO providerDTO = medicalProviderMapper.toDTO(medicalProvider);
        providerDTO.setAgreements(
                agreementRepository.findAllByProviderId(id).stream()
                        .map(agreementMapper::toSimpleDTO)
                        .toList()
        );
        providerDTO.setProfessionals(professionalRepository.findAllProfessionalLinkedWithMedicalProvider(List.of(id))
                .stream()
                .map(professionalMapper::toSimpleInfoDTO)
                .toList());
        providerDTO.setAttachmentFiles(orgAttachmentRepository.findByOrgIdAndNotDeleted(providerDTO.getId()).stream()
                .map(entity -> new FileUploadDTO(entity.getId(),
                        storageService.generateDownloadUrl(entity.getObjectUrl()), entity.getFileName()))
                .toList()
        );
        return providerDTO;
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    @LogExecution
    public void update(UUID id, MedicalProviderDTO dto) {
        MedicalProvider medicalProviderEntity = medicalProviderRepository.findById(id)
                .orElseThrow(
                        () -> new SmaileRuntimeException("Cannot find Medical provider with id = %s".formatted(id)));
        if (!Objects.equals(dto.getName(), medicalProviderEntity.getName())) {
            validateMedicalProviderNameNotExist(dto);
        }
        log.debug("Medical provider before update: {}", medicalProviderEntity.toString());
        medicalProviderMapper.updateEntity(dto, medicalProviderEntity);
        medicalProviderRepository.save(medicalProviderEntity);
        log.debug("Medical provider after update: {}", medicalProviderEntity.toString());
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    @LogExecution
    public void activate(UUID id) {
        MedicalProvider medicalProviderEntity = medicalProviderRepository.findById(id)
                .orElseThrow(
                        () -> new SmaileRuntimeException("Cannot find medical provider with id = %s".formatted(id)));

        if (medicalProviderEntity.getStatus() == OrganizationStatus.ACTIVE) {
            log.warn("Attempted to activate already active medical provider with ID: {}", id);
            throw new SmaileRuntimeException("Medical provider is already active");
        }

        log.debug("Activating medical provider: {} (Current Status: {})",
                medicalProviderEntity.getName(), medicalProviderEntity.getStatus());

        medicalProviderEntity.setStatus(OrganizationStatus.ACTIVE);
        medicalProviderRepository.save(medicalProviderEntity);

        log.info("Successfully activated medical provider: {} (ID: {})",
                medicalProviderEntity.getName(), medicalProviderEntity.getId());
    }

    @Override
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    @LogExecution
    public void deactivate(UUID id) {
        MedicalProvider medicalProviderEntity = medicalProviderRepository.findById(id)
                .orElseThrow(
                        () -> new SmaileRuntimeException("Cannot find medical provider with id = %s".formatted(id)));

        if (medicalProviderEntity.getStatus() == OrganizationStatus.INACTIVE) {
            log.warn("Attempted to deactivate already inactive medical provider with ID: {}", id);
            throw new SmaileRuntimeException("Medical provider is already inactive");
        }

        log.debug("Deactivating medical provider: {} (Current Status: {})",
                medicalProviderEntity.getName(), medicalProviderEntity.getStatus());

        medicalProviderEntity.setStatus(OrganizationStatus.INACTIVE);
        medicalProviderRepository.save(medicalProviderEntity);

        log.info("Successfully deactivated medical provider: {} (ID: {})",
                medicalProviderEntity.getName(), medicalProviderEntity.getId());
    }

    @Override
    public void delete(UUID id) {
        MedicalProvider medicalProviderEntity = medicalProviderRepository.findById(id)
                .orElseThrow(
                        () -> new SmaileRuntimeException("Cannot find medical provider with id = %s".formatted(id)));
        deleteWithAuthCheck(medicalProviderEntity);
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #scopedOrgId, 'organizations:*:read')")
    public Page<MedicalProviderDTO> query(UUID scopedOrgId,
                                          String keyword,
                                          OrganizationStatus status,
                                          String market,
                                          Pageable pageable) {
        Page<MedicalProvider> medicalProviderPage = medicalProviderRepository.findAll(
                (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    if (scopedOrgId != null) {
                        Join<MedicalProvider, Organization> parent = root.join("parent", JoinType.LEFT);
                        predicates.add(cb.equal(parent.get("id"), scopedOrgId));
                    }

                    if (StringUtils.isNotBlank(keyword)) {
                        String pattern = "%" + keyword.toLowerCase() + "%";
                        predicates.add(cb.or(
                                        cb.like(cb.lower(root.get("name")), pattern),
                                        cb.like(cb.lower(root.get("code")), pattern)
                                )
                        );
                    }

                    if (status != null) {
                        predicates.add(cb.equal(root.get("status"), status));
                    }

                    if (StringUtils.isNotBlank(market)) {
                        predicates.add(cb.equal(root.get("market"), market));
                    }

                    predicates.add(cb.isFalse(root.get("isDeleted")));

                    return predicates.stream().reduce(cb::and).orElseGet(cb::conjunction);
                }, pageable);

        List<UUID> ids = medicalProviderPage.stream().map(MedicalProvider::getId).toList();
        Map<UUID, Long> countAgreementMap = agreementRepository.countByProvider(ids);
        Map<UUID, Long> countProfessionalMap = professionalRepository.countByProvider(ids);
        return medicalProviderPage.map(medicalProvider -> {
            MedicalProviderDTO dto = medicalProviderMapper.toDTO(medicalProvider);
            dto.setAgreementCount(countAgreementMap.getOrDefault(medicalProvider.getId(), 0L));
            dto.setProfessionalCount(countProfessionalMap.getOrDefault(medicalProvider.getId(), 0L));
            return dto;
        });
    }

    @Override
    public Page<MedicalProviderDTO> queryForSelection(boolean isGotChildren, Pageable pageable) {
        UUID currenOrg = SecurityContextUtils.getOwnerOrganizationId();
        Page<MedicalProvider> medicalProviderPage;
        if (isGotChildren) {
            Specification<MedicalProvider> spec = MedicalProviderSpecification.withParentId(currenOrg);
            medicalProviderPage = medicalProviderRepository.findAll(spec, pageable);
        } else {
            medicalProviderPage = medicalProviderRepository.findAll(pageable);
            return medicalProviderPage.map(medicalProviderMapper::toDTO);
        }
        return medicalProviderPage.map(medicalProviderMapper::toDTO);
    }

    private void validateMedicalProviderNameNotExist(MedicalProviderDTO dto) {
        organizationRepository.findOne((root, query, cb) ->
                cb.and(
                        cb.equal(root.get("name"), dto.getName()),
                        root.get("type").in(SmaileConstant.MP_CODE_LIST)
                )
        ).ifPresent(mp -> {
            throw new SmaileRuntimeException("Provider name already exists");
        });
    }

    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #orgId, 'organizations:*delete')")
    @LogExecution
    private void deleteWithAuthCheck(MedicalProvider entity) {
        entity.setDeleted(true);
        medicalProviderRepository.save(entity);
    }

}
