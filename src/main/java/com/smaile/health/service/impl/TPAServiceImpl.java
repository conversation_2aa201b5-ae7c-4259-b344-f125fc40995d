package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.OrgAttachment;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.TPA;
import com.smaile.health.domain.User;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.CreateTPARequestMapper;
import com.smaile.health.mapper.TPAMapper;
import com.smaile.health.model.FileUploadDTO;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.TPADTO;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.CreateTPARequestDTO;
import com.smaile.health.repository.OrgAttachmentRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.TPARepository;
import com.smaile.health.repository.specification.TPASpecification;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ObjectStorageService;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.service.TPAService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class TPAServiceImpl implements TPAService {

    private final TPARepository tpaRepository;
    private final TPAMapper tpaMapper;
    private final CreateTPARequestMapper createRequestMapper;
    private final I18nService i18nService;
    private final OrganizationRepository organizationRepository;
    private final UserService userService;
    private final EmailService emailService;
    private final ObjectStorageService objectStorageService;
    private final OrganizationService organizationService;
    private final OrgAttachmentRepository orgAttachmentRepository;

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize(
            "@permissionEvaluator.hasPermission(authentication, T(com.smaile.health.util.SecurityUtils).getCurrentUserOrganizationId().orElse(null), 'organizations:*:read')")
    public Page<TPADTO> search(String market,
                               String status,
                               String name,
                               String registrationNumber,
                               String adminType,
                               String icId,
                               Pageable pageable) {
        long startTime = System.currentTimeMillis();
        log.debug(
                "Starting TPA search operation with filters - Market: {}, Status: {}, Name: {}, Registration Number: {}, Admin Type: {}, IC ID: {}, Page: {}",
                market, status, name, registrationNumber, adminType, icId, pageable.getPageNumber());

        try {
            OrganizationType tpaTypeFilter = SecurityContextUtils.getOwnerOrganizationType();

            UUID currentUserOrgId = null;
            if (!SecurityContextUtils.isSuperAdmin()) {
                currentUserOrgId = SecurityContextUtils.getOwnerOrganizationId();
            }

            Specification<TPA> spec = TPASpecification.searchSpecification(market, status, name, registrationNumber,
                    icId, tpaTypeFilter, adminType, currentUserOrgId);
            Page<TPA> tpas = tpaRepository.findAll(spec, pageable);

            return tpas.map(this::mapToDTOWithRelatedOrgs);
        } catch (Exception e) {
            log.error("TPA search operation failed with error: {}", e.getMessage(), e);
            throw e;
        }
    }

    @LogExecution
    @Override
    @Transactional(readOnly = true)
    @PreAuthorize("@smaileAuthorizationService.hasRole(authentication, #id, 'organizations:*:read')")
    public TPADTO get(UUID id) {
        TPA tpa = tpaRepository.findById(id)
                .orElseThrow(() -> new NotFoundException(
                        i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())
                ));

        TPADTO dto = mapToDTOWithRelatedOrgs(tpa);

        List<OrgAttachment> orgAttachments = orgAttachmentRepository.findByOrgIdAndNotDeleted(tpa.getId());
        dto.setAttachmentFiles(orgAttachments.stream().map(
                        file -> new FileUploadDTO(file.getId(), objectStorageService.generateDownloadUrl(file.getObjectUrl()),
                                file.getFileName())
                ).toList()
        );

        return dto;
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermission(authentication, T(com.smaile.health.util.SecurityUtils).getCurrentUserOrganizationId().orElse(null), 'organizations:*:create')")
    public UUID create(CreateTPARequestDTO requestDTO) {
        log.debug("Creating new TPA: {}", requestDTO.getName());
        long startTime = System.currentTimeMillis();

        try {
            // Validate name duplication (excluding inactive)
            if (tpaRepository.findByNameAndStatusNot(requestDTO.getName(), OrganizationStatus.INACTIVE).isPresent()) {
                log.warn("Attempted to create TPA with duplicate name: {}", requestDTO.getName());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.TPA_NAME_DUPLICATE.getMessageKey())
                );
            }

            // Admin user information is now required and validated at DTO level
            log.debug("Will create new admin user: {} after TPA creation", requestDTO.getAdmin().getEmail());

            // Get current user's organization to set as parent and determine TPA type
            UUID currentUserOrgId = SecurityContextUtils.getOwnerOrganizationId();

            Organization parentOrg = organizationRepository.findById(currentUserOrgId)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.ORGANIZATION_NOT_FOUND.getMessageKey())
                    ));

            // Determine TPA type based on current user's organization type
            OrganizationType tpaType = determineTPAType(parentOrg);

            // Convert request DTO to TPA DTO
            TPADTO tpaDTO = createRequestMapper.toTPADTO(requestDTO);

            TPA tpa = tpaMapper.toEntity(tpaDTO);
            tpa.setId(UUIDv7.generate());
            tpa.setType(tpaType);
            tpa.setStatus(OrganizationStatus.ACTIVE);
            tpa.setParent(parentOrg);

            log.debug("Saving TPA entity with ID: {} and type: {}", tpa.getId(), tpa.getType());
            TPA savedTPA = tpaRepository.save(tpa);

            if (CollectionUtils.isNotEmpty(requestDTO.getAttachmentFiles())) {
                organizationService.saveAttachmentFiles(tpa, requestDTO.getAttachmentFiles());
            }

            // Create admin user (now required)
            UserDTO userDTO = new UserDTO();
            userDTO.setEmail(requestDTO.getAdmin().getEmail());
            userDTO.setUsername(requestDTO.getAdmin().getUsername());
            userDTO.setFullName(requestDTO.getAdmin().getFullName());
            userDTO.setPhone(requestDTO.getAdmin().getPhone());
            userDTO.setRoleCode(determineAdminRoleCode(tpaType));
            userDTO.setStatus(UserStatus.ACTIVE);
            SmaileUserCredential userCredential = userService.createWithPasswordGenerated(savedTPA.getId(), userDTO,
                    false, true);

            emailService.sendOrganizationOnboardNotification(userDTO, savedTPA, userCredential);

            long endTime = System.currentTimeMillis();
            log.info("Successfully created TPA with admin: {} (ID: {}, Type: {}) in {} ms",
                    savedTPA.getName(), savedTPA.getId(), savedTPA.getType(), endTime - startTime);

            return savedTPA.getId();
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to create TPA after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@smaileAuthorizationService.hasPermission(authentication, #id, 'organizations:*:update')")
    public void update(UUID id, TPADTO tpaDTO) {
        log.debug("Updating TPA with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            TPA existingTPA = tpaRepository.findByIdAndStatusNot(id, OrganizationStatus.INACTIVE)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())
                    ));

            if (existingTPA.getStatus() == OrganizationStatus.INACTIVE) {
                log.warn("Attempted to update inactive TPA with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.TPA_CANNOT_UPDATE_INACTIVE.getKey())
                );
            }

            // Validate name duplication (excluding current entity and inactive)
            if (tpaRepository.findByNameAndIdNot(tpaDTO.getName(), id).isPresent()) {
                log.warn("Attempted to update TPA with duplicate name: {}", tpaDTO.getName());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.TPA_NAME_DUPLICATE.getMessageKey())
                );
            }
            log.debug("Updating TPA entity: {} -> {}",
                    existingTPA.getName(), tpaDTO.getName());

            tpaDTO.setId(id);
            // Preserve the original TPA type
            tpaDTO.setType(existingTPA.getType());
            tpaMapper.updateEntityFromDTO(tpaDTO, existingTPA);
            tpaRepository.save(existingTPA);

            long endTime = System.currentTimeMillis();
            log.info("Successfully updated TPA: {} (ID: {}) in {} ms",
                    existingTPA.getName(), existingTPA.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to update TPA with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    public void activate(UUID id) {
        log.debug("Activating TPA with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            TPA tpa = tpaRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())
                    ));

            if (tpa.getStatus() == OrganizationStatus.ACTIVE) {
                log.warn("Attempted to activate already active TPA with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.TPA_ALREADY_ACTIVE.getKey())
                );
            }

            log.debug("Activating TPA: {} (Current Status: {})",
                    tpa.getName(), tpa.getStatus());

            tpa.setStatus(OrganizationStatus.ACTIVE);
            tpaRepository.save(tpa);

            long endTime = System.currentTimeMillis();
            log.info("Successfully activated TPA: {} (ID: {}) in {} ms",
                    tpa.getName(), tpa.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to activate TPA with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    public void deactivate(UUID id) {
        log.debug("Deactivating TPA with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            TPA tpa = tpaRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())
                    ));

            if (tpa.getStatus() == OrganizationStatus.INACTIVE) {
                log.warn("Attempted to deactivate already inactive TPA with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.TPA_ALREADY_INACTIVE.getKey())
                );
            }

            log.debug("Deactivating TPA: {} (Current Status: {})",
                    tpa.getName(), tpa.getStatus());

            tpa.setStatus(OrganizationStatus.INACTIVE);
            tpaRepository.save(tpa);

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated TPA: {} (ID: {}) in {} ms",
                    tpa.getName(), tpa.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate TPA with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@smaileAuthorizationService.hasPermission(authentication, #id, 'organizations:*:delete')")
    public void delete(UUID id) {
        log.debug("Deactivating TPA with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            TPA tpa = tpaRepository.findByIdAndStatusNot(id, OrganizationStatus.INACTIVE)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.TPA_NOT_FOUND.getMessageKey())
                    ));

            if (tpa.getStatus() == OrganizationStatus.INACTIVE) {
                log.warn("Attempted to deactivate already inactive TPA with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.TPA_ALREADY_INACTIVE.getKey())
                );
            }

            log.debug("Deactivating TPA: {} (Current Status: {})",
                    tpa.getName(), tpa.getStatus());

            tpa.setStatus(OrganizationStatus.INACTIVE);
            tpaRepository.save(tpa);

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated TPA: {} (ID: {}) in {} ms",
                    tpa.getName(), tpa.getId(), endTime - startTime);

        } catch (ValidationException | NotFoundException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate TPA with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.TPA_OPERATION_FAILED.getKey()), e
            );
        }
    }

    /**
     * Maps TPA to DTO with total admin count and related organizations
     */
    private TPADTO mapToDTOWithRelatedOrgs(TPA tpa) {
        TPADTO dto = tpaMapper.toDTO(tpa);
        dto.setTotalAdmin(tpa.getUserOrganizations().size());

        // Ensure related organizations are loaded and filtered
        if (tpa.getLinkedOrganizations() != null) {
            List<OrganizationDTO> relatedOrgs = tpa.getLinkedOrganizations().stream()
                    .filter(org -> org.getStatus() != OrganizationStatus.INACTIVE)
                    .map(org -> {
                        OrganizationDTO orgDTO = new OrganizationDTO();
                        orgDTO.setId(org.getId());
                        orgDTO.setName(org.getName());
                        orgDTO.setCode(org.getCode());
                        orgDTO.setType(org.getType().toString());
                        orgDTO.setStatus(org.getStatus());
                        return orgDTO;
                    })
                    .toList();
            dto.setRelatedOrganizations(relatedOrgs);
        }

        // Check if current user has SUPER_SMAILE_ADMIN role to include user information
        List<UserDTO> users = tpa.getUserOrganizations().stream()
                .map(userOrg -> {
                    User user = userOrg.getUser();
                    UserDTO userDTO = new UserDTO();

                    // Only set the required fields
                    userDTO.setEmail(user.getEmail());
                    userDTO.setFullName(user.getFullName());
                    userDTO.setPhone(user.getPhone());

                    // Get role code from UserRole
                    String roleCode = userOrg.getUserRoles().stream()
                            .findFirst()
                            .map(userRole -> userRole.getRole().getCode())
                            .orElse(null);
                    userDTO.setRoleCode(roleCode);

                    return userDTO;
                })
                .toList();

        dto.setUsers(users);
        return dto;
    }

    /**
     * Determines the TPA type based on the parent organization type
     *
     * @param parentOrg the parent organization
     * @return the appropriate TPA type
     * @throws ValidationException if the parent organization type is not allowed to create TPAs
     */
    private OrganizationType determineTPAType(Organization parentOrg) {
        return switch (parentOrg.getType()) {
            case IC -> {
                log.debug("Current user is from IC organization, setting TPA type to IC_TPA");
                yield OrganizationType.IC_TPA;
            }
            case SUPER_SMAILE -> {
                log.debug("Current user is from SUPER_SMAILE organization, setting TPA type to SMAILE_TPA");
                yield OrganizationType.SMAILE_TPA;
            }
            default -> {
                log.error("Current user's organization type {} is not allowed to create TPA", parentOrg.getType());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.INSUFFICIENT_PERMISSIONS_TO_CREATE_TPA.getMessageKey())
                );
            }
        };
    }

    /**
     * Determines the appropriate admin role code based on TPA type
     *
     * @param tpaType the type of TPA
     * @return the role code for the admin user
     */
    private String determineAdminRoleCode(OrganizationType tpaType) {
        return switch (tpaType) {
            case IC_TPA -> RoleEnum.IC_TPA_ADMIN.name();
            case SMAILE_TPA -> RoleEnum.SMAILE_TPA_ADMIN.name();
            default -> {
                log.warn("Unknown TPA type: {}, using default admin role", tpaType);
                yield RoleEnum.IC_TPA_ADMIN.name();
            }
        };
    }

}
