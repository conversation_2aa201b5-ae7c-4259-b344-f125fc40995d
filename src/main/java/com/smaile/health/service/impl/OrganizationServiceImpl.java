package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.domain.OrgAttachment;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.model.FileUploadDTO;
import com.smaile.health.model.OrganizationDTO;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.repository.OrgAttachmentRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.RoleRepository;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ObjectStorageService;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.EnumMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static com.smaile.health.constants.Constants.SMAILE_ADMIN_ORG;

@Service
@Transactional(readOnly = true, rollbackFor = Exception.class)
@Slf4j
@RequiredArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {

    private final OrganizationRepository organizationRepository;
    private final RoleRepository roleRepository;
    private final OrgAttachmentRepository orgAttachmentRepository;
    private final ObjectStorageService storageService;
    private final I18nService i18nService;

    @Override
    public Organization getSmaileOrganization() {
        return organizationRepository.getReferenceById(UUID.fromString(SMAILE_ADMIN_ORG));
    }

    @Override
    public Set<UUID> getOrganizationWithChildrenIds(UUID organizationId) {

        Set<UUID> orgIdList = new HashSet<>(organizationRepository.findByParentIdAndIsDeletedFalse(organizationId)
                .stream()
                .map(Organization::getId)
                .toList());

        orgIdList.add(organizationId);
        return orgIdList;
    }

    @Override
    @LogExecution
    public PageResponse<OrganizationDTO> getAllOrganizationsPaged(Pageable pageable) {
        Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap = buildOrganizationTypeToRoleMap();
        Page<Organization> allOrgsPage = organizationRepository.findAll(pageable);
        Page<OrganizationDTO> dtoPage = allOrgsPage.map(org -> mapToOrganizationDTO(org, organizationTypeToRoleMap));
        return PageResponse.of(dtoPage);
    }

    @Override
    @LogExecution
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #currentOrgId, 'organizations:*:read')")
    public PageResponse<OrganizationDTO> getCurrentOrgWithChildrenPaged(UUID currentOrgId, Pageable pageable) {
        Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap = buildOrganizationTypeToRoleMap();

        Organization currentOrg = organizationRepository.findById(currentOrgId)
                .orElseThrow(() -> new RuntimeException("Current organization not found"));

        Page<Organization> childOrgsPage = organizationRepository.findByParentId(currentOrgId, pageable);

        List<OrganizationDTO> allOrgs = new ArrayList<>();

        allOrgs.add(mapToOrganizationDTO(currentOrg, organizationTypeToRoleMap));

        childOrgsPage.getContent().forEach(childOrg ->
                allOrgs.add(mapToOrganizationDTO(childOrg, organizationTypeToRoleMap)));

        Page<OrganizationDTO> combinedPage = new PageImpl<>(
                allOrgs,
                pageable,
                allOrgs.size()
        );

        return PageResponse.of(combinedPage);
    }

    @LogExecution
    @Override
    public void saveAttachmentFiles(Organization organization, List<FileUploadDTO> fileUploadList) {
        List<OrgAttachment> attachments = new ArrayList<>();
        for (FileUploadDTO file : fileUploadList) {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String persistedObjectLocation = SmaileConstant.ORGANIZATION_FILE_PATH.formatted(organization.getId(),
                    timestamp, file.getFileName());
            storageService.move(file.getObjectLocation(), persistedObjectLocation);
            String fileName = "%s-%s".formatted(timestamp, file.getFileName());
            attachments.add(
                    new OrgAttachment(UUIDv7.generate(), organization, persistedObjectLocation, fileName, false));
        }
        orgAttachmentRepository.saveAll(attachments);
    }

    @Transactional
    @LogExecution
    @Override
    public UUID addAttachment(UUID orgId, FileUploadDTO fileUploadDTO) {
        Organization org = organizationRepository.findById(orgId).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.organization.not-found"))
        );

        // TODO
        // Refactor to use the SecurityContextUtils
        //        if (!SecurityUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())
        //                && (!Objects.equals(org.getParent().getId(), SecurityUtils.getActorOrgId()))
        //                && (!Objects.equals(orgId, SecurityUtils.getActorOrgId()))
        //        ) {
        //            throw new SmaileRuntimeException(i18nService.getMessage("error.permission-denied"));
        //        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String persistedObjectLocation = SmaileConstant.ORGANIZATION_FILE_PATH.formatted(org.getId(), timestamp,
                fileUploadDTO.getFileName());
        storageService.move(fileUploadDTO.getObjectLocation(), persistedObjectLocation);

        String fileName = "%s-%s".formatted(timestamp, fileUploadDTO.getFileName());
        OrgAttachment orgAttachment = new OrgAttachment(UUIDv7.generate(), org, persistedObjectLocation, fileName,
                false);
        orgAttachmentRepository.save(orgAttachment);
        return orgAttachment.getId();
    }

    @LogExecution
    @Transactional
    @Override
    public UUID deleteAttachment(UUID orgId, UUID attachmentId) {
        Organization organization = organizationRepository.findById(orgId).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.organization.not-found"))
        );

        // TODO
        // Refactor to use the SecurityContextUtils
        //        if (!SecurityUtils.hasRole(RoleEnum.SUPER_SMAILE_ADMIN.name())
        //            && !Objects.equals(organization.getParent().getId(), SecurityUtils.getActorOrgId())
        //                && Objects.equals(orgId, SecurityUtils.getActorOrgId())
        //        ) {
        //            throw new SmaileRuntimeException(i18nService.getMessage("error.permission-denied"));
        //        }

        OrgAttachment attachment = orgAttachmentRepository.findById(attachmentId).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.organization.attachment-not-found"))
        );
        attachment.setDeleted(true);
        orgAttachmentRepository.save(attachment);
        return attachment.getId();
    }

    /**
     * Builds a map from organization type to set of roles
     */
    private Map<OrganizationType, Set<RoleEnum>> buildOrganizationTypeToRoleMap() {
        Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap = new EnumMap<>(OrganizationType.class);
        roleRepository.findAll().forEach(role ->
                organizationTypeToRoleMap.compute(role.getOrganizationType(), (k, v) -> {
                    if (v == null) {
                        v = new HashSet<>();
                    }
                    v.add(RoleEnum.resolve(role.getCode()));
                    return v;
                })
        );
        return organizationTypeToRoleMap;
    }

    /**
     * Maps an Organization entity to OrganizationDTO
     */
    private OrganizationDTO mapToOrganizationDTO(Organization org,
                                                 Map<OrganizationType, Set<RoleEnum>> organizationTypeToRoleMap) {
        return OrganizationDTO.builder()
                .id(org.getId())
                .code(org.getCode())
                .name(org.getName())
                .type(org.getType().toString())
                .status(org.getStatus())
                .registrationNumber(org.getRegistrationNumber())
                .contactPhone(org.getContactPhone())
                .contactEmail(org.getContactEmail())
                .address(org.getAddress())
                .orgRoleList(organizationTypeToRoleMap.getOrDefault(org.getType(), Set.of()))
                .parentId(org.getParent() != null ? org.getParent().getId() : null)
                .dateCreated(org.getDateCreated())
                .createdBy(org.getCreatedBy())
                .lastUpdated(org.getLastUpdated())
                .updatedBy(org.getUpdatedBy())
                .build();
    }

}
