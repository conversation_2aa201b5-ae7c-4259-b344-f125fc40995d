package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.config.EmailConfig;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.ProfessionalDocumentType;
import com.smaile.health.constants.Status;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.domain.Professional;
import com.smaile.health.domain.ProfessionalLinkingProjection;
import com.smaile.health.domain.ProfessionalLob;
import com.smaile.health.domain.ProviderProfessional;
import com.smaile.health.domain.Speciality;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.mapper.ProfessionalMapper;
import com.smaile.health.mapper.SpecialityMapper;
import com.smaile.health.model.FileDTO;
import com.smaile.health.model.ProfessionalDTO;
import com.smaile.health.model.ProfessionalDocumentDTO;
import com.smaile.health.model.ProfessionalSummaryDTO;
import com.smaile.health.model.RegisterProfessionalFormDTO;
import com.smaile.health.model.RegisterProfessionalPresignedUrl;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.ProfessionalBulkLinkRequest;
import com.smaile.health.model.response.MedicalProviderResponse;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.model.response.ProfessionalLinkingResponse;
import com.smaile.health.repository.MedicalProviderRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.ProfessionalLobRepository;
import com.smaile.health.repository.ProfessionalRepository;
import com.smaile.health.repository.ProviderProfessionalRepository;
import com.smaile.health.repository.SpecialityRepository;
import com.smaile.health.repository.specification.ProfessionalSpecification;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ObjectStorageService;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.service.ProfessionalService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.EmailUtils;
import com.smaile.health.util.UUIDv7;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static com.smaile.health.util.PasswordUtils.generateTemporaryPassword;

@Slf4j
@Service
@AllArgsConstructor
public class ProfessionalServiceImpl implements ProfessionalService {

    // TODO verify the format dir
    private static final String PROFESSIONAL = "professional";
    private static final String PROFESSIONAL_DIR_FORMAT = "%s/%s/%s";
    private static final String PROFESSIONAL_DIR_NOT_EXT_FORMAT = "%s/%s/%s";
    private final OrganizationRepository organizationRepository;
    private final ProfessionalRepository professionalRepository;
    private final ProviderProfessionalRepository providerProfessionalRepository;
    private final ProfessionalLobRepository professionalLobRepository;
    private final MedicalProviderRepository medicalProviderRepository;
    private final SpecialityRepository specialityRepository;

    private final ProfessionalMapper professionalMapper;
    private final SpecialityMapper specialityMapper;
    private final MedicalProviderMapper medicalProviderMapper;
    private final OrganizationService organizationService;
    private final ObjectStorageService objectStorageService;
    private final UserService userService;
    private final I18nService i18nService;
    private final EmailUtils emailUtils;
    private final EmailConfig emailConfig;

    @Override
    @Transactional
    public UUID create(RegisterProfessionalFormDTO registerProfessionalForm) throws IOException {
        // Validate if Professional with same primary license id exists
        if (professionalRepository.existsByPrimaryLicenseId(registerProfessionalForm.getPrimaryLicenseId())) {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_LICENSE_ID_EXISTED.getKey(),
                            registerProfessionalForm.getPrimaryLicenseId())
            );
        }

        if (professionalRepository.existsByContactEmail(registerProfessionalForm.getEmail())) {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_EMAIL_EXISTED.getKey(),
                            registerProfessionalForm.getEmail())
            );
        }

        if (professionalRepository.existsByUsername(registerProfessionalForm.getUsername())) {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_USERNAME_EXISTED.getKey(),
                            registerProfessionalForm.getUsername())
            );
        }

        Professional professional = createProfessional(registerProfessionalForm);
        professionalRepository.save(professional);
        List<ProfessionalLob> professionalLobs = createProfessionalLob(registerProfessionalForm, professional);
        professionalLobRepository.saveAll(professionalLobs);
        professional.setProfessionalLobs(professionalLobs);

        // Send registration confirmation email
        try {
            String[] nameParts = registerProfessionalForm.getFullProfessionalName().split(" ", 2);
            String firstName = nameParts.length > 0 ? nameParts[0] : registerProfessionalForm.getFullProfessionalName();
            String lastName = nameParts.length > 1 ? nameParts[1] : "";

            emailUtils.sendProfessionalRegistrationConfirmationEmail(
                    registerProfessionalForm.getEmail(),
                    firstName,
                    lastName,
                    emailConfig.getSupport().getEmail(),
                    emailConfig.getSupport().getPhone()
            );
            log.info("Registration confirmation email sent to: {}", registerProfessionalForm.getEmail());
        } catch (Exception e) {
            log.error("Failed to send registration confirmation email to {}: {}",
                    registerProfessionalForm.getEmail(), e.getMessage(), e);
            // Don't fail the registration if email fails
        }

        return professional.getId();
    }

    @Override
    public List<RegisterProfessionalPresignedUrl> createPreSignedUrl() throws IOException {
        return Arrays.stream(ProfessionalDocumentType.values()).map(documentType -> {
            OffsetDateTime expiryTime = OffsetDateTime.now(ZoneOffset.UTC).plusMinutes(15);
            String objectName = PROFESSIONAL_DIR_NOT_EXT_FORMAT.formatted(PROFESSIONAL, UUID.randomUUID(),
                    documentType);
            String uploadUrl = objectStorageService.generateUploadPresignedUrl(objectName, expiryTime);
            return RegisterProfessionalPresignedUrl.builder()
                    .fileType(documentType)
                    .objectName(objectName)
                    .uploadUrl(uploadUrl)
                    .expiredAt(Instant.from(expiryTime))
                    .build();
        }).toList();
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #parentId, 'professionals:*:read')")
    public PageResponse<ProfessionalDTO> query(String search, List<Filter> filters, Pageable pageable) {
        Specification<Professional> spec =
                ProfessionalSpecification
                        .searchProfessional(search)
                        .and(ProfessionalSpecification.withFilter(filters));
        Page<Professional> page = professionalRepository.findAll(spec, pageable);
        Page<ProfessionalDTO> pageResponse = page.map(this::toProfessionalDTO);
        return PageResponse.of(pageResponse);
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #parentId, 'professionals:*:read')")
    public ProfessionalDTO detail(UUID professionalId) {
        Professional professional = professionalRepository.findById(professionalId)
                .orElseThrow(() -> new ValidationException(
                        i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey(), professionalId.toString())
                ));
        return toProfessionalDTO(professional);
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #parentId, 'professionals:*:update')")
    @Transactional
    public ProfessionalDTO approve(UUID professionalId) {
        Professional professional = professionalRepository.findById(professionalId).orElseThrow(() ->
                new ValidationException(
                        i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey())
                )
        );
        if (OrganizationStatus.PENDING.equals(professional.getStatus())) {
            String password = generateTemporaryPassword(8);
            // Create KC user and User as Admin in Professional Org
            UUID userId = userService.createProfessionalUser(professional.getId(),
                    UserDTO.builder()
                            .username(professional.getUsername())
                            .email(professional.getContactEmail())
                            .fullName(professional.getFullName())
                            .status(UserStatus.ACTIVE)
                            .build(),
                    password);
            log.info("Create User id {} in Professional Org {} ", userId, professional.getId());
            int result = organizationRepository.updateStaus(professionalId, OrganizationStatus.ACTIVE,
                    OrganizationStatus.PENDING);
            log.debug("Applied {} record status change id = {} {} -> {} ", result, professionalId,
                    OrganizationStatus.PENDING, OrganizationStatus.ACTIVE);

            // Send approval email
            try {
                String[] nameParts = professional.getFullProfessionalName().split(" ", 2);
                String firstName = nameParts.length > 0 ? nameParts[0] : professional.getFullProfessionalName();
                String lastName = nameParts.length > 1 ? nameParts[1] : "";

                emailUtils.sendProfessionalApprovalEmail(
                        professional.getContactEmail(),
                        firstName,
                        lastName,
                        professional.getContactEmail(), password,
                        "APPROVED",
                        null
                );
                log.info("Approval email sent to: {}", professional.getContactEmail());
            } catch (Exception e) {
                log.error("Failed to send approval email to {}: {}",
                        professional.getContactEmail(), e.getMessage(), e);
                // Don't fail the approval if email fails
            }
        } else {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_INVALID_STATUS.getKey())
            );
        }
        return detail(professionalId);
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #parentId, 'professionals:*:update')")
    @Transactional
    public ProfessionalDTO deny(UUID professionalId) {
        Professional professional = professionalRepository.findById(professionalId).orElseThrow(() ->
                new ValidationException(
                        i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey())
                )
        );
        if (OrganizationStatus.PENDING.equals(professional.getStatus())) {
            int result = organizationRepository.updateStaus(professionalId, OrganizationStatus.REJECTED,
                    OrganizationStatus.PENDING);
            log.debug("Applied {} record status change id = {} {} -> {} ", result, professionalId,
                    OrganizationStatus.PENDING, OrganizationStatus.REJECTED);

            // Send denial email
            try {
                String[] nameParts = professional.getFullProfessionalName().split(" ", 2);
                String firstName = nameParts.length > 0 ? nameParts[0] : professional.getFullProfessionalName();
                String lastName = nameParts.length > 1 ? nameParts[1] : "";

                emailUtils.sendProfessionalApprovalEmail(
                        professional.getContactEmail(),
                        firstName,
                        lastName, "", "",
                        "REJECTED",
                        "Your application did not meet our current requirements. Please review and resubmit with additional information."
                );
                log.info("Denial email sent to: {}", professional.getContactEmail());
            } catch (Exception e) {
                log.error("Failed to send denial email to {}: {}",
                        professional.getContactEmail(), e.getMessage(), e);
                // Don't fail the denial if email fails
            }
        } else {
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_INVALID_STATUS.getKey())
            );
        }
        return detail(professionalId);
    }

    @Override
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #parentId, 'professionals:*:read')")
    public ProfessionalSummaryDTO summary() {
        UUID currentOrganizationId = SecurityContextUtils.getOwnerOrganizationId();
        return ProfessionalSummaryDTO.builder()
                .activeCount(professionalRepository.countByStatusAndParentId(OrganizationStatus.ACTIVE,
                        currentOrganizationId))
                .pendingCount(professionalRepository.countByStatusAndParentId(OrganizationStatus.PENDING,
                        currentOrganizationId))
                .specialtyCount(professionalRepository.countDistinctSpecialities())
                .totalCount(professionalRepository.countByParentId(currentOrganizationId))
                .build();
    }

    @LogExecution
    @Override
    @Transactional(readOnly = true)
    public Page<ProfessionalLinkingResponse> queryForLinking(Boolean queryLinked,
                                                             String keyword,
                                                             String country,
                                                             String speciality,
                                                             Pageable pageable) {
        UUID orgId = SecurityContextUtils.getOwnerOrganizationId();
        Page<ProfessionalLinkingProjection> projectionPage;
        if (Boolean.TRUE.equals(queryLinked)) {
            projectionPage = providerProfessionalRepository.findAllByLinkedMedicalProvider(orgId, keyword, country,
                    speciality, pageable);
        } else if (Boolean.FALSE.equals(queryLinked)) {
            projectionPage = providerProfessionalRepository.findAllUnlinkedToMedicalProvider(orgId, keyword, country,
                    speciality, pageable);
        } else {
            projectionPage = providerProfessionalRepository.findAllWithOptionalMedicalProvider(orgId, keyword, country,
                    speciality, pageable);
        }
        log.debug(projectionPage.toString());
        return projectionPage.map(projection -> ProfessionalLinkingResponse.builder()
                .professional(professionalMapper.toDTO(projection.getProfessional()))
                .linked(projection.getLinked())
                .build()
        );
    }

    @Override
    @Transactional
    @LogExecution
    public UUID linkToActorOrg(UUID professionalId) {
        UUID currentOrganizationId = SecurityContextUtils.getOwnerOrganizationId();
        medicalProviderRepository.findById(currentOrganizationId).orElseThrow(
                () -> new SmaileRuntimeException(
                        "Cannot find any medical provider with actor's organization id = %s".formatted(
                                currentOrganizationId))
        );

        ProviderProfessional link = providerProfessionalRepository.findByProviderIdAndProfessionalId(
                        currentOrganizationId,
                        professionalId)
                .orElse(null);
        if (link != null) {
            link.setStatus(Status.ACTIVE);
        } else {
            link = ProviderProfessional.builder()
                    .id(UUIDv7.generate()).providerId(currentOrganizationId).professionalId(professionalId)
                    .status(Status.ACTIVE)
                    .build();
        }
        providerProfessionalRepository.save(link);
        Professional professional = professionalRepository.findById(professionalId).orElseThrow(
                () -> new SmaileRuntimeException("Cannot find professional %s".formatted(professionalId))
        );
        professional.setLastUpdated(OffsetDateTime.now());
        return link.getId();
    }

    @Override
    @Transactional
    @LogExecution
    public UUID unlinkFromActorOrg(UUID professionalId) {
        UUID currentOrganizationId = SecurityContextUtils.getOwnerOrganizationId();
        medicalProviderRepository.findById(currentOrganizationId).orElseThrow(
                () -> new SmaileRuntimeException(
                        "Cannot find any medical provider with actor's organization id = %s".formatted(
                                currentOrganizationId))
        );
        ProviderProfessional providerProfessionalLink = providerProfessionalRepository.findByProviderIdAndProfessionalId(
                        currentOrganizationId, professionalId)
                .orElseThrow(() -> new SmaileRuntimeException("Cannot find any relational to unlink"));
        providerProfessionalLink.setStatus(Status.INACTIVE);
        providerProfessionalRepository.save(providerProfessionalLink);
        Professional professional = professionalRepository.findById(professionalId).orElseThrow(
                () -> new SmaileRuntimeException("Cannot find professional %s".formatted(professionalId))
        );
        professional.setLastUpdated(OffsetDateTime.now());
        professionalRepository.save(professional);
        return providerProfessionalLink.getId();
    }

    @Override
    public void bulkLinkToActorOrg(ProfessionalBulkLinkRequest request) {
        UUID currentOrganizationId = SecurityContextUtils.getOwnerOrganizationId();
        medicalProviderRepository.findById(currentOrganizationId).orElseThrow(
                () -> new SmaileRuntimeException(
                        "Cannot find any medical provider with actor's organization id = %s".formatted(
                                currentOrganizationId))
        );
        request.getProfessionalList().forEach(this::linkToActorOrg);
    }

    private Professional createProfessional(RegisterProfessionalFormDTO registerForm) {
        Professional professional = new Professional();
        professional.setId(UUIDv7.generate());
        professional.setParent(organizationService.getSmaileOrganization());

        // Professional Info
        professional.setCountry(registerForm.getCountry());
        professional.setFullProfessionalName(registerForm.getFullProfessionalName());
        professional.setSpecialities(retrieveSpeciality(registerForm));
        professional.setLicenses(registerForm.getProfessionalLicenses());
        professional.setPrimaryLicenseId(registerForm.getPrimaryLicenseId());
        professional.setPrimaryPracticeMarket(registerForm.getPrimaryPracticeMarket());
        professional.setMarketSegment(registerForm.getMarketSegment());

        // Base Org info
        professional.setContactEmail(registerForm.getEmail());
        professional.setName(registerForm.getFullProfessionalName());
        professional.setContactPhone(registerForm.getPhoneNumber());
        professional.setRegistrationNumber(registerForm.getPrimaryLicenseId());
        professional.setUsername(registerForm.getUsername());
        professional.setFullName(registerForm.getFullName());

        professional.setStatus(OrganizationStatus.PENDING);
        professional.setType(OrganizationType.PROFESSIONAL);
        return professional;
    }

    private List<ProfessionalLob> createProfessionalLob(RegisterProfessionalFormDTO registerProfessionalForm,
                                                        Professional professional) throws IOException {
        List<ProfessionalLob> lobs = new ArrayList<>();
        UUID uuid = professional.getId();
        if (hasNotEmpty(registerProfessionalForm.getProfessionalLicenseFile())) {
            String newPath = PROFESSIONAL_DIR_FORMAT.formatted(PROFESSIONAL, uuid, ProfessionalDocumentType.LICENSE,
                    registerProfessionalForm.getProfessionalLicenseFile().getFileExtension());
            objectStorageService.move(registerProfessionalForm.getProfessionalLicenseFile().getFileName(), newPath);
            lobs.add(createProfessionalLob(registerProfessionalForm.getProfessionalLicenseFile(), newPath,
                    ProfessionalDocumentType.LICENSE));
        }
        if (hasNotEmpty(registerProfessionalForm.getEducationDiplomaFile())) {
            String newPath = PROFESSIONAL_DIR_FORMAT.formatted(PROFESSIONAL, uuid,
                    ProfessionalDocumentType.EDUCATION_DIPLOMA,
                    registerProfessionalForm.getEducationDiplomaFile().getFileExtension());
            objectStorageService.move(registerProfessionalForm.getEducationDiplomaFile().getFileName(), newPath);
            lobs.add(createProfessionalLob(registerProfessionalForm.getEducationDiplomaFile(), newPath,
                    ProfessionalDocumentType.EDUCATION_DIPLOMA));
        }
        if (hasNotEmpty(registerProfessionalForm.getProfessionalCertificateFile())) {
            String newPath = PROFESSIONAL_DIR_FORMAT.formatted(PROFESSIONAL, uuid, ProfessionalDocumentType.CERTIFICATE,
                    registerProfessionalForm.getProfessionalCertificateFile().getFileExtension());
            objectStorageService.move(registerProfessionalForm.getProfessionalCertificateFile().getFileName(), newPath);
            lobs.add(createProfessionalLob(registerProfessionalForm.getProfessionalCertificateFile(), newPath,
                    ProfessionalDocumentType.CERTIFICATE));
        }
        if (hasNotEmpty(registerProfessionalForm.getLiabilityInsuranceFile())) {
            String newPath = PROFESSIONAL_DIR_FORMAT.formatted(PROFESSIONAL, uuid,
                    ProfessionalDocumentType.LIABILITY_INSURANCE,
                    registerProfessionalForm.getLiabilityInsuranceFile().getFileExtension());
            objectStorageService.move(registerProfessionalForm.getLiabilityInsuranceFile().getFileName(), newPath);
            lobs.add(createProfessionalLob(registerProfessionalForm.getLiabilityInsuranceFile(), newPath,
                    ProfessionalDocumentType.LIABILITY_INSURANCE));
        }
        if (hasNotEmpty(registerProfessionalForm.getPhotoFile())) {
            String newPath = PROFESSIONAL_DIR_FORMAT.formatted(PROFESSIONAL, uuid, ProfessionalDocumentType.PHOTO,
                    registerProfessionalForm.getPhotoFile().getFileExtension());
            objectStorageService.move(registerProfessionalForm.getPhotoFile().getFileName(), newPath);
            lobs.add(createProfessionalLob(registerProfessionalForm.getPhotoFile(), newPath,
                    ProfessionalDocumentType.PHOTO));
        }
        lobs.forEach(professionalLob -> professionalLob.setProfessional(professional));
        return lobs;
    }

    private boolean hasNotEmpty(FileDTO file) {
        if (file == null) {
            return false;
        }
        if (!StringUtils.hasText(file.getFileName())) {
            return false;
        }
        boolean uploaded = objectStorageService.verifyUploaded(file.getFileName());
        if (!uploaded) {
            log.warn("File {} is not uploaded", file.getFileName());
            throw new ValidationException(
                    i18nService.getMessage(MessageKey.FILE_NOT_UPLOADED.getKey(), file.getFileName())
            );
        }
        return uploaded;
    }

    private ProfessionalLob createProfessionalLob(FileDTO fileDto, String file, ProfessionalDocumentType documentType)
            throws IOException {
        ProfessionalLob lob = new ProfessionalLob();
        lob.setId(UUIDv7.generate());
        lob.setFileType(fileDto.getFileExtension());
        lob.setFileName(file);
        lob.setObjectUrl(file);
        lob.setDocumentType(documentType);
        return lob;
    }

    private ProfessionalDTO toProfessionalDTO(Professional professional) {
        ProfessionalDTO professionalDTO = professionalMapper.toDTO(professional);
        professionalDTO.setSpecialities(
                professional.getSpecialities().stream()
                        .map(specialityMapper::toDTO)
                        .toList()
        );
        if (professional.getProfessionalLobs() != null) {
            professionalDTO.setDocuments(
                    professional.getProfessionalLobs().stream()
                            .map(professionalLob -> {
                                ProfessionalDocumentDTO doc = new ProfessionalDocumentDTO();
                                doc.setUrl(objectStorageService.generateDownloadUrl(professionalLob.getFileName()));
                                doc.setDocumentType(professionalLob.getDocumentType());
                                doc.setFileExtension(professionalLob.getFileType());
                                return doc;
                            })
                            .toList()
            );
        }

        return professionalDTO;
    }

    private List<Speciality> retrieveSpeciality(RegisterProfessionalFormDTO registerProfessionalFormDTO) {
        if (registerProfessionalFormDTO.getProfessionalSpecialties() != null) {
            return specialityRepository.findByNameInAndIsActiveTrue(
                    registerProfessionalFormDTO.getProfessionalSpecialties());
        } else if (registerProfessionalFormDTO.getSpecialties() != null) {
            return specialityRepository.findAllById(registerProfessionalFormDTO.getSpecialties());
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #professionalId, 'organizations:*:update')")
    public void activate(UUID professionalId) {
        log.debug("Activating professional with ID: {}", professionalId);
        long startTime = System.currentTimeMillis();

        try {
            Professional professional = professionalRepository.findById(professionalId)
                    .orElseThrow(() -> new ValidationException(
                            i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey())
                    ));

            if (professional.getStatus() == OrganizationStatus.ACTIVE) {
                log.warn("Attempted to activate already active professional with ID: {}", professionalId);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.PROFESSIONAL_ALREADY_ACTIVE.getKey())
                );
            }

            log.debug("Activating professional: {} (Current Status: {})",
                    professional.getFullProfessionalName(), professional.getStatus());

            professional.setStatus(OrganizationStatus.ACTIVE);
            professionalRepository.save(professional);

            long endTime = System.currentTimeMillis();
            log.info("Successfully activated professional: {} (ID: {}) in {} ms",
                    professional.getFullProfessionalName(), professional.getId(), endTime - startTime);

        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to activate professional with ID {} after {} ms: {}",
                    professionalId, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_INVALID_STATUS.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #professionalId, 'organizations:*:update')")
    public void deactivate(UUID professionalId) {
        log.debug("Deactivating professional with ID: {}", professionalId);
        long startTime = System.currentTimeMillis();

        try {
            Professional professional = professionalRepository.findById(professionalId)
                    .orElseThrow(() -> new ValidationException(
                            i18nService.getMessage(MessageKey.PROFESSIONAL_NOT_FOUND.getKey())
                    ));

            if (professional.getStatus() == OrganizationStatus.INACTIVE) {
                log.warn("Attempted to deactivate already inactive professional with ID: {}", professionalId);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.PROFESSIONAL_ALREADY_INACTIVE.getKey())
                );
            }

            log.debug("Deactivating professional: {} (Current Status: {})",
                    professional.getFullProfessionalName(), professional.getStatus());

            professional.setStatus(OrganizationStatus.INACTIVE);
            professionalRepository.save(professional);

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated professional: {} (ID: {}) in {} ms",
                    professional.getFullProfessionalName(), professional.getId(), endTime - startTime);

        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate professional with ID {} after {} ms: {}",
                    professionalId, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.PROFESSIONAL_INVALID_STATUS.getKey()), e
            );
        }
    }

    @Override
    public Page<MedicalProviderResponse> getLinkedMedicalProviders(Pageable pageable) {
        UUID professionalId = SecurityContextUtils.getOwnerOrganizationId();
        Page<MedicalProvider> mpPage = medicalProviderRepository.findByLinkedProfessional(professionalId, pageable);
        return mpPage.map(medicalProviderMapper::toResponse);
    }

}
