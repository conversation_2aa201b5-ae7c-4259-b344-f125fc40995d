package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.InsuredStatusEnum;
import com.smaile.health.domain.Insured;
import com.smaile.health.domain.InsuredQueryProjection;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.mapper.InsuranceCompanyMapper;
import com.smaile.health.mapper.InsuredMapper;
import com.smaile.health.mapper.ModalityMapper;
import com.smaile.health.model.InsuredDTO;
import com.smaile.health.model.response.InsuredResponse;
import com.smaile.health.repository.InsuranceCompanyRepository;
import com.smaile.health.repository.InsuredRepository;
import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.InsuredService;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.UUID;

@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class InsuredServiceImpl implements InsuredService {

    private final InsuredMapper mapper;
    private final InsuredRepository insuredRepository;
    private final I18nService i18nService;
    private final InsuranceCompanyRepository icRepository;
    private final ModalityRepository modalityRepository;
    private final ModalityMapper modalityMapper;
    private final InsuranceCompanyMapper insuranceCompanyMapper;

    @Transactional
    @LogExecution
    @Override
    public UUID createInsured(InsuredDTO insuredDTO) {
        modalityRepository.findById(insuredDTO.getModalityId()).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.modality.not-found"))
        );
        icRepository.findById(insuredDTO.getIcOrgId()).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.insurance-company.not-found"))
        );
        insuredRepository.findByIcOrgIdAndInsuredCodeAndIsDeletedFalse(insuredDTO.getIcOrgId(),
                        insuredDTO.getInsuredCode())
                .map(Insured::getStatus)
                .filter(status -> status == InsuredStatusEnum.ACTIVE)
                .ifPresent(status -> {
                    throw new SmaileRuntimeException(i18nService.getMessage("error.insured.active-insured-exist"));
                });
        Insured insured = mapper.toEntity(insuredDTO);
        insured.setId(UUIDv7.generate());
        insured.setStatus(InsuredStatusEnum.ACTIVE);
        insuredRepository.save(insured);
        return insured.getId();
    }

    //TODO: add authorization
    @LogExecution
    @Transactional
    @Override
    public UUID updateInsured(InsuredDTO dto) {
        Insured insuredEntity = insuredRepository.findByIdAndIsDeletedFalse(dto.getId()).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.insured.not-found"))
        );
        if (insuredEntity.getStatus() != InsuredStatusEnum.ACTIVE) {
            throw new SmaileRuntimeException(i18nService.getMessage("error.insured.update-denied"));
        }
        mapper.update(dto, insuredEntity);
        if (insuredEntity.getCoverageBeginDate().isAfter(insuredEntity.getCoverageEndDate())) {
            throw new SmaileRuntimeException(i18nService.getMessage("error.insured.coverage-range-invalid"));
        }
        insuredRepository.save(insuredEntity);
        return insuredEntity.getId();
    }

    @Override
    public InsuredDTO getById(UUID id) {
        Insured insured = insuredRepository.findById(id).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.insured.not-found"))
        );
        InsuredDTO insuredDTO = mapper.toDTO(insured);
        insuredDTO.setModality(modalityMapper.entityToDto(insured.getModality()));
        insuredDTO.setIcOrg(insuranceCompanyMapper.toDTO(insured.getIcOrg()));
        return insuredDTO;
    }

    @LogExecution
    @Override
    public Page<InsuredResponse> query(String keyword,
                                       InsuredStatusEnum status,
                                       LocalDate coverageBeginDate,
                                       LocalDate coverageEndDate,
                                       Pageable pageable) {
        if (coverageBeginDate != null && coverageEndDate != null && coverageBeginDate.isAfter(coverageEndDate)) {
            throw new SmaileRuntimeException(i18nService.getMessage("error.insured.coverage-range-invalid"));
        }

        UUID organizationId = SecurityContextUtils.getOwnerOrganizationId();
        Page<InsuredQueryProjection> insuredProjectionPage = insuredRepository.query(organizationId,
                keyword, status, coverageBeginDate, coverageEndDate, pageable);
        return insuredProjectionPage.map(mapper::toResponse);
    }

    @LogExecution
    @Transactional
    @Override
    public UUID activeInsured(UUID id) {
        Insured insured = insuredRepository.findByIdAndIsDeletedFalse(id).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.insured.not-found"))
        );
        insured.setStatus(InsuredStatusEnum.ACTIVE);
        insured.setStatusDate(null);
        insured.setStatusReason(null);
        insuredRepository.save(insured);
        return insured.getId();
    }

    @LogExecution
    @Transactional
    @Override
    public UUID suspendInsured(UUID id, LocalDate effectiveDate, String reason) {
        Insured insured = insuredRepository.findByIdAndIsDeletedFalse(id).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.insured.not-found"))
        );
        if (insured.getStatus() != InsuredStatusEnum.ACTIVE) {
            throw new SmaileRuntimeException(i18nService.getMessage("error.insured.not-active"));
        }
        if (effectiveDate.isBefore(insured.getCoverageBeginDate()) || effectiveDate.isAfter(
                insured.getCoverageEndDate())) {
            throw new SmaileRuntimeException(i18nService.getMessage("error.insured.effective-date-not-in-range"));
        }
        insured.setStatus(InsuredStatusEnum.SUSPENDED);
        insured.setStatusDate(effectiveDate);
        insured.setStatusReason(reason);
        insuredRepository.save(insured);
        return insured.getId();
    }

    @LogExecution
    @Transactional
    @Override
    public UUID cancelInsured(UUID id, LocalDate effectiveDate, String reason) {
        Insured insured = insuredRepository.findByIdAndIsDeletedFalse(id).orElseThrow(
                () -> new SmaileRuntimeException(i18nService.getMessage("error.insured.not-found"))
        );
        if (effectiveDate.isBefore(insured.getCoverageBeginDate()) || effectiveDate.isAfter(
                insured.getCoverageEndDate())) {
            throw new SmaileRuntimeException(i18nService.getMessage("error.insured.effective-date-not-in-range"));
        }
        insured.setStatus(InsuredStatusEnum.CANCELLED);
        insured.setStatusDate(effectiveDate);
        insured.setStatusReason(reason);
        insuredRepository.save(insured);
        return insured.getId();
    }

}
