package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.Modality;
import com.smaile.health.domain.ModalityAgreements;
import com.smaile.health.domain.id.ModalityAgreementId;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.AgreementMapper;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.response.ModalityAgreementResponseDTO;
import com.smaile.health.repository.AgreementRepository;
import com.smaile.health.repository.MedicalProviderRepository;
import com.smaile.health.repository.ModalityAgreementRepository;
import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.repository.specification.ModalityAgreementSpecification;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityAgreementService;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@AllArgsConstructor
public class ModalityAgreementServiceImpl implements ModalityAgreementService {

    private final I18nService i18nService;
    private final AgreementMapper agreementMapper;
    private final MedicalProviderMapper medicalProviderMapper;

    private final ModalityAgreementRepository modalityAgreementRepository;
    private final ModalityRepository modalityRepository;
    private final AgreementRepository agreementRepository;
    private final MedicalProviderRepository medicalProviderRepository;

    @Override
    @Transactional
    public void linkAgreement(UUID modalityId, UUID agreementId) {
        validate(modalityId, agreementId);
        boolean existed = modalityAgreementRepository.existsById_ModalityIdAndId_AgreementId(modalityId, agreementId);
        if (existed) {
            throw new ValidationException(MessageKey.MODALITY_AGREEMENT_AGREEMENT_EXISTED.getMessageKey());
        }
        Modality modality = modalityRepository.getReferenceById(modalityId);
        Agreement agreement = agreementRepository.getReferenceById(agreementId);
        UUID medicalProviderId = agreement.getProvider().getId();

        // TODO
        // Review the update for get owner organization
        boolean hasPermission = medicalProviderRepository.existsByIdAndParentId(medicalProviderId,
                SecurityContextUtils.getOwnerOrganizationId());
        if (!hasPermission) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.PERMISSION_DENIED.getMessageKey()));
        }

        ModalityAgreements modalityAgreement = new ModalityAgreements();
        modalityAgreement.setId(new ModalityAgreementId(modalityId, agreementId));
        modalityAgreement.setAgreement(agreement);
        modalityAgreement.setModality(modality);
        modalityAgreementRepository.save(modalityAgreement);
        // TODO: log the delete action
        // TODO: remove or check procedures linked to this agreement
    }

    @Override
    @Transactional
    public void unlinkAgreement(UUID modalityId, UUID agreementId) {
        validate(modalityId, agreementId);
        boolean existed = modalityAgreementRepository.existsById_ModalityIdAndId_AgreementId(modalityId, agreementId);
        if (!existed) {
            throw new ValidationException(MessageKey.MODALITY_AGREEMENT_AGREEMENT_NOT_EXISTED.getMessageKey());
        }
        Agreement agreement = agreementRepository.getReferenceById(agreementId);
        UUID medicalProviderId = agreement.getProvider().getId();

        // TODO
        // Review the update for get owner organization
        boolean hasPermit = medicalProviderRepository.existsByIdAndParentId(medicalProviderId,
                SecurityContextUtils.getOwnerOrganizationId());
        if (!hasPermit) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.PERMISSION_DENIED.getMessageKey()));
        }
        modalityAgreementRepository.deleteById(new ModalityAgreementId(modalityId, agreementId));
        // TODO: log the delete action
        // TODO: remove or check procedures linked to this agreement
    }

    @Override
    public Page<ModalityAgreementResponseDTO> query(String search, UUID modalityId, Pageable pageable) {
        Specification<ModalityAgreements> specification = ModalityAgreementSpecification
                .fetch()
                .and(ModalityAgreementSpecification.search(search))
                .and(ModalityAgreementSpecification.withModalityId(modalityId));
        Page<ModalityAgreements> page = modalityAgreementRepository.findAll(specification, pageable);
        return page.map(this::toResponse);
    }

    private void validate(UUID modalityId, UUID agreementId) {
        if (!modalityRepository.existsById(modalityId)) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_NOT_FOUND.getMessageKey(), modalityId));
        }
        if (!agreementRepository.existsById(agreementId)) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey(), modalityId));
        }
    }

    private ModalityAgreementResponseDTO toResponse(ModalityAgreements entity) {
        ModalityAgreementResponseDTO dto = new ModalityAgreementResponseDTO();
        AgreementDTO agreementDTO = agreementMapper.toDTO(entity.getAgreement());
        agreementDTO.setMedicalProvider(medicalProviderMapper.toSimpleDTO(entity.getAgreement().getProvider()));
        dto.setAgreement(agreementDTO);

        dto.setDateCreated(entity.getDateCreated());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setUpdatedBy(entity.getUpdatedBy());
        dto.setLastUpdated(entity.getLastUpdated());
        return dto;
    }

}
