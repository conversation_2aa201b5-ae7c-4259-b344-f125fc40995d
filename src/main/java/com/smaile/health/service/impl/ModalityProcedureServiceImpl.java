package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.NetworkType;
import com.smaile.health.domain.Diagnosis;
import com.smaile.health.domain.Modality;
import com.smaile.health.domain.ModalityDiagnosis;
import com.smaile.health.domain.ModalityProcedure;
import com.smaile.health.domain.ModalityProcedureCoverage;
import com.smaile.health.domain.Procedure;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.ModalityProcedureCoverageMapper;
import com.smaile.health.mapper.ModalityProcedureMapper;
import com.smaile.health.model.request.CoverageRequestDTO;
import com.smaile.health.model.request.CreateModalityProcedureRequestDTO;
import com.smaile.health.model.response.ModalityProcedureResponseDTO;
import com.smaile.health.repository.ModalityDiagnosisRepository;
import com.smaile.health.repository.ModalityProcedureCoverageRepository;
import com.smaile.health.repository.ModalityProcedureRepository;
import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.repository.ProcedureRepository;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityProcedureService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ModalityProcedureServiceImpl implements ModalityProcedureService {

    private final ModalityRepository modalityRepository;

    private final ProcedureRepository procedureRepository;

    private final ModalityProcedureMapper modalityProcedureMapper;

    private final ModalityDiagnosisRepository modalityDiagnosisRepository;

    private final ModalityProcedureRepository modalityProcedureRepository;

    private final ModalityProcedureCoverageMapper modalityProcedureCoverageMapper;

    private final ModalityProcedureCoverageRepository modalityProcedureCoverageRepository;

    private final I18nService i18nService;

    @Override
    @LogExecution
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#requestDTO.modalityId)")
    @Transactional
    public UUID create(CreateModalityProcedureRequestDTO requestDTO) {
        Modality modality = modalityRepository.findById(requestDTO.getModalityId()).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_NOT_FOUND.getMessageKey())
                )
        );

        Procedure procedure = procedureRepository.findByCodeAndIsActiveTrue(requestDTO.getProcedureCode()).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey())
                )
        );

        Optional<ModalityProcedure> modalityProcedureExist = modalityProcedureRepository.findByModalityIdAndProcedureId(modality.getId(), procedure.getId());
        if (modalityProcedureExist.isPresent()) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_PROCEDURE_ALREADY_EXISTS.getMessageKey())
            );
        }

        Set<CoverageRequestDTO> validatedCoverages = validateCoverages(modality.getNetworkType(), requestDTO.getCoverages());

        // add procedure
        ModalityProcedure modalityProcedure = modalityProcedureMapper.dtoToEntity(requestDTO).configureWithIdAnd(modality, procedure);
        // add modality diagnosis
        for (Diagnosis diagnosis : procedure.getRelatedDiagnoses()) {

            ModalityDiagnosis modalityDiagnosis = modalityDiagnosisRepository.findByModalityIdAndDiagnosisId(modality.getId(), diagnosis.getId()).orElseGet(
                    () -> ModalityDiagnosis.fromProcedureDto(modality, diagnosis, requestDTO)
            );

            modalityProcedure.addModalityDiagnosis(modalityDiagnosis);
        }

        ModalityProcedure insertedModalityProcedure = modalityProcedureRepository.save(modalityProcedure);

        List<ModalityProcedureCoverage> coverages = validatedCoverages.stream().map(
                coverageRequestDTO -> {
                    return modalityProcedureCoverageMapper.requestDtoToEntity(coverageRequestDTO).configureWithIdAndProcedure(insertedModalityProcedure);
                }
        ).toList();

        modalityProcedureCoverageRepository.saveAll(coverages);

        return insertedModalityProcedure.getId();
    }

    @Override
    @LogExecution
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.ownsProcedure(#modalityProcedureId)")
    @Transactional
    public void delete(UUID modalityProcedureId) {
        ModalityProcedure modalityProcedure = modalityProcedureRepository.findById(modalityProcedureId).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_PROCEDURE_NOT_FOUND.getMessageKey())
                )
        );

        Set<ModalityDiagnosis> linkedModalityDiagnoses = new HashSet<>(modalityProcedure.getModalityDiagnoses());

        // remove related coverages.
        modalityProcedureCoverageRepository.deleteByIdIn(
                modalityProcedure.getCoverages().stream().map(ModalityProcedureCoverage::getId).collect(Collectors.toSet())
        );

        modalityProcedure.getModalityDiagnoses().clear();
        modalityProcedureRepository.delete(modalityProcedure);

        modalityDiagnosisRepository.deleteOrphansDiagnosis(linkedModalityDiagnoses);

    }

    @Override
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#modalityId)")
    @Transactional
    public Page<ModalityProcedureResponseDTO> query(String keyword, UUID modalityId, Pageable pageable) {

        Page<UUID> pageIds = modalityProcedureRepository.searchToFindIds(keyword, modalityId, pageable);

        List<ModalityProcedureResponseDTO> responseDTOs = modalityProcedureRepository.loadModalityProcedureSummary(pageIds.getContent())
                .stream().map(modalityProcedureMapper::entityToDTOResponse).toList();

        return new PageImpl<>(responseDTOs, pageable, pageIds.getTotalElements());

    }

    private Set<CoverageRequestDTO> validateCoverages(NetworkType networkType, List<CoverageRequestDTO> coverages) {

        if (networkType.equals(NetworkType.ALL)) {
            return new HashSet<>(coverages.stream().collect(
                    Collectors.toMap(CoverageRequestDTO::getNetworkType, coverage -> coverage, (c1, c2) -> c1)
            ).values());
        }

        return coverages.stream().filter(coverage -> coverage.getNetworkType().equals(networkType)).collect(Collectors.toSet());
    }

}
