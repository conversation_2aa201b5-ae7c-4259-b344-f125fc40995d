package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.ModalityDiagnosisType;
import com.smaile.health.constants.NetworkType;
import com.smaile.health.domain.Diagnosis;
import com.smaile.health.domain.Modality;
import com.smaile.health.domain.ModalityDiagnosis;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.ModalityDiagnosisMapper;
import com.smaile.health.model.ModalityDiagnosisId;
import com.smaile.health.model.request.CreateModalityDiagnosisRequestDTO;
import com.smaile.health.model.response.ModalityDiagnosisResponseDTO;
import com.smaile.health.repository.DiagnosisRepository;
import com.smaile.health.repository.ModalityDiagnosisRepository;
import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityDiagnosisService;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ModalityDiagnosisServiceImpl implements ModalityDiagnosisService {

    private final ModalityRepository modalityRepository;

    private final DiagnosisRepository diagnosisRepository;

    private final I18nService i18nService;

    private final ModalityDiagnosisMapper modalityDiagnosisMapper;

    private final ModalityDiagnosisRepository modalityDiagnosisRepository;

    @Override
    @Transactional
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#dto.modalityId)")
    public UUID create(CreateModalityDiagnosisRequestDTO dto) {

        Modality modality = modalityRepository.findById(dto.getModalityId()).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_NOT_FOUND.getMessageKey())
                )
        );

        Diagnosis diagnosis = diagnosisRepository.findByCodeAndIsActiveTrue(dto.getDiagnosisCode()).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.DIAGNOSIS_NOT_FOUND.getMessageKey())
                )
        );

        Optional<ModalityDiagnosis> modalityDiagnosisExist = modalityDiagnosisRepository.findByModalityIdAndDiagnosisId(
                modality.getId(),
                diagnosis.getId()
        );
        if (modalityDiagnosisExist.isPresent()) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_DIAGNOSIS_ALREADY_EXISTS.getMessageKey())
            );
        }

        if (! modality.getNetworkType().equals(NetworkType.ALL) && ! modality.getNetworkType().equals(dto.getNetworkType())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_NETWORK_TYPE_DONT_MATCH.getMessageKey())
            );
        }

        ModalityDiagnosis modalityDiagnosis = modalityDiagnosisMapper.dtoToEntity(dto).configureAsOrphan(diagnosis, modality);

        ModalityDiagnosis insertedModalityDiagnosis = modalityDiagnosisRepository.save(modalityDiagnosis);

        return insertedModalityDiagnosis.getId();
    }

    @Override
    @Transactional
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.ownsDiagnosis(#modalityDiagnosisId)")
    public void delete(UUID modalityDiagnosisId) {
        ModalityDiagnosis modalityDiagnosis = modalityDiagnosisRepository.findById(modalityDiagnosisId).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_DIAGNOSIS_NOT_FOUND.getMessageKey())
                )
        );

        if (modalityDiagnosis.getType().equals(ModalityDiagnosisType.LINKED)) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_DIAGNOSIS_UNLINK_NOT_ALLOWED.getMessageKey())
            );
        }

        modalityDiagnosisRepository.delete(modalityDiagnosis);
    }

    @Override
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#modalityId)")
    public Page<ModalityDiagnosisResponseDTO> query(String keyword, UUID modalityId, Pageable pageable) {

        Page<ModalityDiagnosisId> pageIds = modalityDiagnosisRepository.searchToFindIds(keyword, modalityId, pageable);

        // Map <modality_diagnosis_id, diagnosis_id>
        Map<UUID, UUID> modalityDiagnosisMap = pageIds.getContent().stream()
                .collect(Collectors.toMap(ModalityDiagnosisId::getId, ModalityDiagnosisId::getDiagnosisId));

        // Load modality with procedure
        List<ModalityDiagnosis> modalityDiagnoses = modalityDiagnosisRepository.loadModalityDiagnosisWithProcedure(
                new ArrayList<>(modalityDiagnosisMap.keySet())
        );

        // Load diagnosis with speciality
        Map<UUID, Diagnosis> diagnoses = diagnosisRepository.loadWithSpeciality(new ArrayList<>(modalityDiagnosisMap.values()))
                .stream().collect(Collectors.toMap(Diagnosis::getId, Function.identity()));

        // Aggregate data
        List<ModalityDiagnosisResponseDTO> responseDTOs = modalityDiagnoses.stream().map(
                modalityDiagnosis -> {

                    modalityDiagnosis.setDiagnosis(diagnoses.get(modalityDiagnosisMap.get(modalityDiagnosis.getId())));

                    return modalityDiagnosisMapper.entityToResponseDTO(modalityDiagnosis);
                }
        ).toList();

        return new PageImpl<>(responseDTOs, pageable, pageIds.getTotalElements());
    }

}
