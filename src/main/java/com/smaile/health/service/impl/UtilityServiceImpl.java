package com.smaile.health.service.impl;

import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.mapper.ProofMapper;
import com.smaile.health.mapper.SpecialityMapper;
import com.smaile.health.model.CountryDTO;
import com.smaile.health.model.MarketDTO;
import com.smaile.health.model.MarketSegmentDTO;
import com.smaile.health.model.ProofDTO;
import com.smaile.health.model.SpecialityDTO;
import com.smaile.health.model.response.GeneratePresignedUrlResponse;
import com.smaile.health.repository.CountryRepository;
import com.smaile.health.repository.MarketRepository;
import com.smaile.health.repository.MarketSegmentRepository;
import com.smaile.health.repository.ProofRepository;
import com.smaile.health.repository.SpecialityRepository;
import com.smaile.health.service.ObjectStorageService;
import com.smaile.health.service.UtilityService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
public class UtilityServiceImpl implements UtilityService {

    private final MarketRepository marketRepository;
    private final MarketSegmentRepository marketSegmentRepository;

    private final ObjectStorageService storageService;
    private final CountryRepository countryRepository;
    private final SpecialityRepository specialityRepository;
    private final ProofRepository proofRepository;
    private final ProofMapper proofMapper;
    private final SpecialityMapper specialityMapper;

    @Override
    public List<MarketDTO> getAllMarket() {
        return marketRepository.findAll().stream()
                .map(entity -> MarketDTO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<CountryDTO> getAllCountry() {
        return countryRepository.findAll().stream()
                .map(entity -> CountryDTO.builder().id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<MarketSegmentDTO> getAllMarketSegment() {
        return marketSegmentRepository.findAll().stream()
                .map(entity -> MarketSegmentDTO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .code(entity.getCode())
                        .build())
                .toList();
    }

    @Override
    public List<SpecialityDTO> getAllProfessionalSpecialty() {
        return specialityRepository.findByIsActiveTrue().stream()
                .map(specialityMapper::toDTO)
                .toList();
    }

    @Override
    public List<SpecialityDTO> getAllAuthorizedSpecialties() {
        return specialityRepository.findByIsActiveTrue().stream()
                .map(specialityMapper::toDTO)
                .toList();
    }

    @Override
    public List<ProofDTO> getAllProofs() {
        return proofRepository.findByIsActiveTrue().stream()
                .map(proofMapper::toDTO)
                .toList();
    }

    @Override
    public GeneratePresignedUrlResponse generatePresignedUrl() {
        String fileLocation = SmaileConstant.TMP_FILE_PATH.formatted(UUID.randomUUID());
        OffsetDateTime expiryTime = OffsetDateTime.now().plusMinutes(SmaileConstant.DEFAULT_FILE_UPLOAD_EXPIRY_MINUTES);

        String uploadUrl = storageService.generateUploadPresignedUrl(fileLocation, expiryTime);

        return new GeneratePresignedUrlResponse(uploadUrl, fileLocation, expiryTime);
    }
}
