package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.AgreementProceduresDetail;
import com.smaile.health.domain.MedicalProvider;
import com.smaile.health.domain.Procedure;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.AgreementMapper;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.mapper.ProcedureMapper;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.AgreementProcedureDTO;
import com.smaile.health.model.request.AgreementProcedureRequest;
import com.smaile.health.model.request.CreateAgreementDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.UpdateAgreementDTO;
import com.smaile.health.model.request.UpdateAgreementProcedureRequest;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.repository.AgreementRepository;
import com.smaile.health.repository.MedicalProviderRepository;
import com.smaile.health.repository.ProcedureRepository;
import com.smaile.health.repository.specification.AgreementSpecification;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.AgreementService;
import com.smaile.health.service.I18nService;
import com.smaile.health.util.UUIDv7;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.smaile.health.util.DateTimeUtils.isBetween;

@Service
@AllArgsConstructor
public class AgreementServiceImpl implements AgreementService {

    private final I18nService i18nService;
    private final MedicalProviderRepository medicalProviderRepository;
    private final AgreementRepository agreementRepository;
    private final ProcedureRepository procedureRepository;
    private final AgreementMapper agreementMapper;
    private final MedicalProviderMapper medicalProviderMapper;
    private final ProcedureMapper procedureMapper;

    @Override
    public PageResponse<AgreementDTO> query(String search, List<Filter> filters, Pageable pageable) {
        OrganizationType type = SecurityContextUtils.getOwnerOrganizationType();
        Specification<Agreement> spec = AgreementSpecification
                .search(search)
                .and(AgreementSpecification.withFilter(filters));
        if (Objects.equals(OrganizationType.IC, type)) {
            spec.and(AgreementSpecification.withChildMedicalProvider(SecurityContextUtils.getOwnerOrganizationId()));
        }
        Page<Agreement> page = agreementRepository.findAll(spec, pageable);
        List<UUID> procedureIds = page.getContent().stream().flatMap(a -> a.getProceduresDetails().stream())
                .map(AgreementProceduresDetail::getProcedureId).distinct().toList();

        List<Procedure> proceduresWithProofs = procedureRepository.findAllById(procedureIds);
        Map<UUID, Procedure> procedureMap = proceduresWithProofs.stream()
                .collect(Collectors.toMap(Procedure::getId, Function.identity()));
        Page<AgreementDTO> pageResponse = page.map(agreement -> toAgreementDTO(agreement, procedureMap));
        return PageResponse.of(pageResponse);
    }

    @Override
    public AgreementDTO detail(UUID agreementId) {
        Agreement agreement = agreementRepository.findOne(AgreementSpecification.fetchOne(agreementId)).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())));

        List<UUID> procedureIds = agreement.getProceduresDetails().stream()
                .map(AgreementProceduresDetail::getProcedureId).distinct().toList();

        Map<UUID, Procedure> procedureMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(agreement.getProceduresDetails())) {
            List<Procedure> proceduresWithProofs = procedureRepository.findAllById(procedureIds);
            procedureMap = proceduresWithProofs.stream()
                    .collect(Collectors.toMap(Procedure::getId, Function.identity()));
        }

        return toAgreementDTO(agreement, procedureMap);
    }

    @Override
    @Transactional
    public UUID create(CreateAgreementDTO agreementRequest) {
        MedicalProvider provider = medicalProviderRepository.findById(agreementRequest.getProviderId()).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.MP_NOT_FOUND.getMessageKey())));
        if (agreementRequest.getEffectiveTime().isAfter(agreementRequest.getCancellationTime())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey()));
        }
        if (agreementRepository.existsByContractId(agreementRequest.getContractId())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.CONTRACT_ID_DUPLICATE.getMessageKey()));
        }
        if (!provider.getParent().getId().equals(SecurityContextUtils.getOwnerOrganizationId())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.NO_AUTHORIZED_CREATE_AGREEMENT_WITH_MP.getMessageKey(),
                            provider.getName()));
        }

        Agreement agreement = toAgreement(agreementRequest);
        agreement.setId(UUIDv7.generate());
        agreement.setProvider(provider);
        agreement.setStatus(Status.ACTIVE);
        if (agreementRequest.getProcedures() != null) {
            List<Procedure> procedures = procedureRepository.findAllById(
                    agreementRequest.getProcedures().stream().map(AgreementProcedureRequest::getProcedureId).toList());
            Map<UUID, Procedure> procedureMap = procedures.stream()
                    .collect(Collectors.toMap(Procedure::getId, Function.identity()));
            // TODO: I think we can compare the size of procedures and agreementRequest.getProcedures()
            // to determine the invalid for generic error message
            agreementRequest.getProcedures().forEach(agreementProcedureRequest -> {
                if (!procedureMap.containsKey(agreementProcedureRequest.getProcedureId())) {
                    throw new ValidationException(i18nService.getMessage(ErrorCode.PROCEDURE_NOT_FOUND.getMessageKey(),
                            agreementProcedureRequest.getProcedureId()));
                }
            });
            Map<UUID, BigDecimal> procedureCostMap = agreementRequest.getProcedures().stream().collect(
                    Collectors.toMap(AgreementProcedureRequest::getProcedureId, AgreementProcedureRequest::getCost));
            // Directly add procedure details using the helper method
            for (Procedure procedure : procedures) {
                BigDecimal cost = procedureCostMap.get(procedure.getId());
                agreement.addProcedureDetail(procedure, cost);
            }
        }
        Agreement savedAgreement = agreementRepository.save(agreement);
        return savedAgreement.getId();
    }

    @Override
    @Transactional
    public void update(UUID id, UpdateAgreementDTO agreementRequest) {
        Agreement agreement = agreementRepository.findById(id).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())));
        if (agreementRequest.getEffectiveTime().isAfter(agreementRequest.getCancellationTime())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey()));
        }

        List<Agreement> agreementsSameContractId = agreementRepository.findAllByContractId(
                agreementRequest.getContractId());
        agreementsSameContractId.removeIf(a -> a.getId().equals(id));
        if (!agreementsSameContractId.isEmpty()) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.CONTRACT_ID_DUPLICATE.getMessageKey()));
        }

        agreement.setContractId(agreementRequest.getContractId());
        agreement.setStatus(agreementRequest.getStatus());
        agreement.setShortDescription(agreementRequest.getShortDescription());
        agreement.setLongDescription(agreementRequest.getLongDescription());
    }

    @Override
    @Transactional
    public void updateProcedure(UUID id, UpdateAgreementProcedureRequest agreementRequest) {
        Agreement agreement = agreementRepository.findById(id).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())));

        List<Procedure> procedures = procedureRepository.findAllById(
                agreementRequest.getProcedures().stream().map(AgreementProcedureRequest::getProcedureId).toList());
        Map<Procedure, BigDecimal> procedureCostMap = new HashMap<>();
        for (Procedure procedure : procedures) {
            for (AgreementProcedureRequest procedureRequest : agreementRequest.getProcedures()) {
                if (procedureRequest.getProcedureId().equals(procedure.getId())) {
                    procedureCostMap.put(procedure, procedureRequest.getCost());
                }
            }
        }
        agreement.updateProceduresDetails(procedureCostMap);
    }

    private AgreementDTO toAgreementDTO(Agreement agreement, Map<UUID, Procedure> procedureMap) {
        AgreementDTO agreementDTO = agreementMapper.toDTO(agreement);
        if (agreement.getProceduresDetails() != null) {
            agreementDTO.setProcedures(agreement.getProceduresDetails().stream().map(procedureDetail -> {
                AgreementProcedureDTO procedureDTO = new AgreementProcedureDTO();
                UUID procedureId = procedureDetail.getProcedureId();
                procedureDTO.setProcedureId(procedureId);
                procedureDTO.setCost(procedureDetail.getCost());
                procedureDTO.setProcedureInfo(procedureMapper.toLiteDDTO(procedureMap.get(procedureId)));
                return procedureDTO;
            }).toList());
        }
        if (agreement.getProvider() != null) {
            agreementDTO.setMedicalProvider(medicalProviderMapper.toSimpleDTO(agreement.getProvider()));
        }
        if (!isBetween(Instant.now(), agreement.getEffectiveTime(), agreement.getCancellationTime())) {
            agreementDTO.setStatus(Status.INACTIVE);
        }

        return agreementDTO;
    }

    private Agreement toAgreement(CreateAgreementDTO agreement) {
        return agreementMapper.toEntity(agreement);
    }

}
