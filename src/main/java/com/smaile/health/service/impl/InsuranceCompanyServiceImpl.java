package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.MessageKey;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.User;
import com.smaile.health.exception.InternalServerException;
import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.CreateInsuranceCompanyRequestMapper;
import com.smaile.health.mapper.InsuranceCompanyMapper;
import com.smaile.health.model.request.CreateInsuranceCompanyRequestDTO;
import com.smaile.health.model.InsuranceCompanyDTO;
import com.smaile.health.model.InsuranceCompanyGeneralInfoDTO;
import com.smaile.health.model.InsuranceCompanySearchDTO;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.repository.InsuranceCompanyRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.EmailService;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.InsuranceCompanyService;
import com.smaile.health.service.OrganizationService;
import com.smaile.health.service.UserService;
import com.smaile.health.util.UUIDv7;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class InsuranceCompanyServiceImpl implements InsuranceCompanyService {

    private final InsuranceCompanyRepository insuranceCompanyRepository;
    private final InsuranceCompanyMapper insuranceCompanyMapper;
    private final CreateInsuranceCompanyRequestMapper createRequestMapper;
    private final I18nService i18nService;
    private final OrganizationRepository organizationRepository;
    private final UserService userService;
    private final OrganizationService organizationService;
    private final EmailService emailService;

    @Override
    @Transactional(readOnly = true)
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, T(com.smaile.health.security.util.SecurityContextUtils).getOwnerOrganizationId(), 'organizations:*:read')")
    public Page<InsuranceCompanySearchDTO> search(String market, String status, String name, Pageable pageable) {
        log.debug("Starting optimized search operation with filters - Market: {}, Status: {}, Name: {}, Page: {}",
                market, status, name, pageable.getPageNumber());

        try {
            boolean shouldSeeAll = SecurityContextUtils.isSuperAdmin();

            UUID currentUserOrgId = null;
            if (!shouldSeeAll) {
                currentUserOrgId = SecurityContextUtils.getOwnerOrganizationId();
            }

            // Use the new custom repository method with projection for efficient data fetching
            Page<InsuranceCompanySearchDTO> result = insuranceCompanyRepository.searchWithConditions(
                    market, status, name, currentUserOrgId, pageable);

            log.debug("Search completed - Found {} results", result.getNumberOfElements());
            return result;
        } catch (Exception e) {
            log.error("Search operation failed with error: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    //    @PreAuthorize("@smaileAuthorizationService.hasPermission(authentication, #id, 'organizations:*:read')")
    public InsuranceCompanyDTO get(UUID id) {
        log.debug("Fetching insurance company with ID: {}", id);

        try {
            InsuranceCompany insuranceCompany = insuranceCompanyRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            return enrichInsuranceCompanyDTO(insuranceCompany);
        } catch (NotFoundException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to retrieve insurance company with ID {}: {}", id, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermission(authentication, T(com.smaile.health.util.SecurityUtils).getCurrentUserOrganizationId().orElse(null), 'organizations:*:create')")
    public UUID create(CreateInsuranceCompanyRequestDTO requestDTO) {
        log.debug("Creating new insurance company: {}", requestDTO.getName());
        long startTime = System.currentTimeMillis();

        try {
            // Validate name duplication (excluding archived)
            if (insuranceCompanyRepository.findByNameAndStatusNot(requestDTO.getName(), OrganizationStatus.INACTIVE).isPresent()) {
                log.warn("Attempted to create insurance company with duplicate name: {}", requestDTO.getName());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NAME_DUPLICATE.getMessageKey())
                );
            }

            log.debug("Will create new admin user: {} after insurance company creation",
                    requestDTO.getAdmin().getEmail());

            UUID currentUserOrgId = SecurityContextUtils.getOwnerOrganizationId();

            Organization parentOrg = organizationRepository.findById(currentUserOrgId)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.ORGANIZATION_NOT_FOUND.getMessageKey())
                    ));

            // Convert request DTO to insurance company DTO
            InsuranceCompanyDTO insuranceCompanyDTO = createRequestMapper.toInsuranceCompanyDTO(requestDTO);

            InsuranceCompany insuranceCompany = insuranceCompanyMapper.toEntity(insuranceCompanyDTO);
            insuranceCompany.setId(UUIDv7.generate());
            insuranceCompany.setType(OrganizationType.IC);
            insuranceCompany.setStatus(OrganizationStatus.ACTIVE);
            insuranceCompany.setParent(parentOrg);

            log.debug("Saving insurance company entity with ID: {}", insuranceCompany.getId());
            InsuranceCompany savedInsuranceCompany = insuranceCompanyRepository.save(insuranceCompany);

            // Create admin user (now required)
            log.debug("Creating new admin user in insurance company: {}", requestDTO.getAdmin().getEmail());
            UserDTO userDTO = new UserDTO();
            userDTO.setEmail(requestDTO.getAdmin().getEmail());
            userDTO.setUsername(requestDTO.getAdmin().getUsername());
            userDTO.setOrganizationId(savedInsuranceCompany.getId());
            userDTO.setFullName(requestDTO.getAdmin().getFullName());
            userDTO.setPhone(requestDTO.getAdmin().getPhone());
            userDTO.setRoleCode(RoleEnum.IC_ADMIN.name());
            userDTO.setStatus(UserStatus.ACTIVE);

            // Automatically create user with invitation email
            SmaileUserCredential userCredential = userService.createWithPasswordGenerated(savedInsuranceCompany.getId(),
                    userDTO, false, true);
            emailService.sendOrganizationOnboardNotification(userDTO, savedInsuranceCompany, userCredential);
            log.debug("Created new admin user: {} in insurance company: {} with automatic invitation",
                    userCredential.getId(), savedInsuranceCompany.getId());

            long endTime = System.currentTimeMillis();
            log.info("Successfully created insurance company with admin: {} (ID: {}) in {} ms",
                    savedInsuranceCompany.getName(), savedInsuranceCompany.getId(), endTime - startTime);

            return savedInsuranceCompany.getId();
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to create insurance company after {} ms: {}", endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize("@smaileAuthorizationService.hasPermission(authentication, #id, 'organizations:*:update')")
    public void update(UUID id, InsuranceCompanyDTO insuranceCompanyDTO) {
        log.debug("Updating insurance company with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            InsuranceCompany existingInsuranceCompany = insuranceCompanyRepository.findByIdAndStatusNot(id, OrganizationStatus.INACTIVE)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            if (existingInsuranceCompany.getStatus() == OrganizationStatus.INACTIVE) {
                log.warn("Attempted to update inactive insurance company with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.INSURANCE_COMPANY_CANNOT_UPDATE_INACTIVE.getKey())
                );
            }

            // Validate name duplication (excluding current entity and inactive)
            if (insuranceCompanyRepository.findByNameAndIdNot(insuranceCompanyDTO.getName(), id).isPresent()) {
                log.warn("Attempted to update insurance company with duplicate name: {}", insuranceCompanyDTO.getName());
                throw new ValidationException(
                        i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NAME_DUPLICATE.getMessageKey())
                );
            }
            log.debug("Updating insurance company entity: {} -> {}",
                    existingInsuranceCompany.getName(), insuranceCompanyDTO.getName());

            insuranceCompanyDTO.setId(id);
            insuranceCompanyMapper.updateEntityFromDTO(insuranceCompanyDTO, existingInsuranceCompany);
            existingInsuranceCompany.setType(OrganizationType.IC);
            insuranceCompanyRepository.save(existingInsuranceCompany);

            long endTime = System.currentTimeMillis();
            log.info("Successfully updated insurance company: {} (ID: {}) in {} ms",
                    existingInsuranceCompany.getName(), existingInsuranceCompany.getId(), endTime - startTime);
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to update insurance company with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    public void activate(UUID id) {
        log.debug("Activating insurance company with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            InsuranceCompany insuranceCompany = insuranceCompanyRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            if (insuranceCompany.getStatus() == OrganizationStatus.ACTIVE) {
                log.warn("Attempted to activate already active insurance company with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.INSURANCE_COMPANY_ALREADY_ACTIVE.getKey())
                );
            }

            log.debug("Activating insurance company: {} (Current Status: {})",
                    insuranceCompany.getName(), insuranceCompany.getStatus());

            insuranceCompany.setStatus(OrganizationStatus.ACTIVE);
            insuranceCompanyRepository.save(insuranceCompany);

            long endTime = System.currentTimeMillis();
            log.info("Successfully activated insurance company: {} (ID: {}) in {} ms",
                    insuranceCompany.getName(), insuranceCompany.getId(), endTime - startTime);
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to activate insurance company with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("@permissionEvaluator.hasPermission(authentication, #id, 'organizations:*:update')")
    public void deactivate(UUID id) {
        log.debug("Deactivating insurance company with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            InsuranceCompany insuranceCompany = insuranceCompanyRepository.findById(id)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            if (insuranceCompany.getStatus() == OrganizationStatus.INACTIVE) {
                log.warn("Attempted to deactivate already inactive insurance company with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.INSURANCE_COMPANY_ALREADY_INACTIVE.getKey())
                );
            }

            log.debug("Deactivating insurance company: {} (Current Status: {})",
                    insuranceCompany.getName(), insuranceCompany.getStatus());

            insuranceCompany.setStatus(OrganizationStatus.INACTIVE);
            insuranceCompanyRepository.save(insuranceCompany);

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated insurance company: {} (ID: {}) in {} ms",
                    insuranceCompany.getName(), insuranceCompany.getId(), endTime - startTime);
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate insurance company with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(rollbackFor = { Exception.class })
    @PreAuthorize(
            "@smaileAuthorizationService.hasPermissionInOrganization(authentication, #id, 'organizations:*:delete')")
    public void delete(UUID id) {
        log.debug("Deactivating insurance company with ID: {}", id);
        long startTime = System.currentTimeMillis();

        try {
            InsuranceCompany insuranceCompany = insuranceCompanyRepository.findByIdAndStatusNot(id, OrganizationStatus.INACTIVE)
                    .orElseThrow(() -> new NotFoundException(
                            i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())
                    ));

            if (insuranceCompany.getStatus() == OrganizationStatus.INACTIVE) {
                log.warn("Attempted to deactivate already inactive insurance company with ID: {}", id);
                throw new ValidationException(
                        i18nService.getMessage(MessageKey.INSURANCE_COMPANY_ALREADY_INACTIVE.getKey())
                );
            }

            log.debug("Deactivating insurance company: {} (Current Status: {})",
                    insuranceCompany.getName(), insuranceCompany.getStatus());

            insuranceCompany.setStatus(OrganizationStatus.INACTIVE);
            insuranceCompanyRepository.save(insuranceCompany);

            long endTime = System.currentTimeMillis();
            log.info("Successfully deactivated insurance company: {} (ID: {}) in {} ms",
                    insuranceCompany.getName(), insuranceCompany.getId(), endTime - startTime);
        } catch (ValidationException | NotFoundException | SmaileRuntimeException e) {
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to deactivate insurance company with ID {} after {} ms: {}",
                    id, endTime - startTime, e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    @Override
    @Transactional(readOnly = true)
    //    @PreAuthorize(
    //            "@smaileAuthorizationService.hasPermission(authentication, T(com.smaile.health.util.SecurityUtils).getCurrentUserOrganizationId().orElse(null), 'organizations:*:read')")
    public List<InsuranceCompanyGeneralInfoDTO> getActiveInsuranceCompanies() {
        log.debug("Fetching list of active insurance companies");
        long startTime = System.currentTimeMillis();

        try {
            // Get current user's roles for role-based filtering
            boolean shouldSeeAll = SecurityContextUtils.isSuperAdmin();

            List<InsuranceCompany> activeInsuranceCompanies;
            if (shouldSeeAll) {
                // SUPER_SMAILE_ADMIN can see all active insurance companies
                activeInsuranceCompanies = insuranceCompanyRepository.findByStatus(OrganizationStatus.ACTIVE);
                log.debug("User has SUPER_SMAILE_ADMIN role, showing all active insurance companies");
            } else {
                // Regular users can only see insurance companies associated with their organization
                UUID currentUserOrgId = SecurityContextUtils.getOwnerOrganizationId();

                // Find insurance companies where parent organization is the current user's org or its children
                Set<UUID> orgIds = organizationService.getOrganizationWithChildrenIds(currentUserOrgId);
                activeInsuranceCompanies = insuranceCompanyRepository.findByStatusAndParentIdIn(
                        OrganizationStatus.ACTIVE,
                        new ArrayList<>(orgIds)
                );
                log.debug("User is restricted to organization {}, showing {} associated insurance companies",
                        currentUserOrgId, activeInsuranceCompanies.size());
            }

            List<InsuranceCompanyGeneralInfoDTO> result = activeInsuranceCompanies.stream()
                    .map(ic -> InsuranceCompanyGeneralInfoDTO.builder()
                            .id(ic.getId())
                            .name(ic.getName())
                            .build())
                    .toList();

            long endTime = System.currentTimeMillis();
            log.debug(
                    "Successfully retrieved {} active insurance companies in {} ms with role-based filtering: shouldSeeAll={}",
                    result.size(), endTime - startTime, shouldSeeAll);

            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("Failed to retrieve active insurance companies after {} ms: {}", endTime - startTime,
                    e.getMessage(), e);
            throw new InternalServerException(
                    i18nService.getMessage(MessageKey.INSURANCE_COMPANY_OPERATION_FAILED.getKey()), e
            );
        }
    }

    /**
     * Enriches InsuranceCompany DTO with additional data based on user role
     */
    private InsuranceCompanyDTO enrichInsuranceCompanyDTO(InsuranceCompany insuranceCompany) {
        InsuranceCompanyDTO dto = insuranceCompanyMapper.toDTO(insuranceCompany);

        // Set total admin count
        dto.setTotalAdmin(insuranceCompany.getUserOrganizations().size());

        // Check if current user has SUPER_SMAILE_ADMIN role
        if (SecurityContextUtils.isSuperAdmin()) {
            List<UserDTO> users = insuranceCompany.getUserOrganizations().stream()
                    .map(userOrg -> {
                        User user = userOrg.getUser();
                        UserDTO userDTO = new UserDTO();

                        // Only set the required fields
                        userDTO.setEmail(user.getEmail());
                        userDTO.setFullName(user.getFullName());
                        userDTO.setPhone(user.getPhone());

                        // Get role code from UserRole
                        String roleCode = userOrg.getUserRoles().stream()
                                .findFirst()
                                .map(userRole -> userRole.getRole().getCode())
                                .orElse(null);
                        userDTO.setRoleCode(roleCode);

                        return userDTO;
                    })
                    .toList();

            dto.setUsers(users);
        } else {
            dto.setUsers(null);
        }

        return dto;
    }

}
