package com.smaile.health.service.impl;

import com.smaile.health.aop.LogExecution;
import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.domain.Modality;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.ModalityMapper;
import com.smaile.health.mapper.OrganizationMapper;
import com.smaile.health.model.ModalitySearchCriteria;
import com.smaile.health.model.TotalDiagnosisModality;
import com.smaile.health.model.TotalProcedureModality;
import com.smaile.health.model.request.CreateModalityRequestDTO;
import com.smaile.health.model.request.UpdateModalityRequestDTO;
import com.smaile.health.model.response.DetailModalityResponse;
import com.smaile.health.model.response.ModalitySummaryResponse;
import com.smaile.health.repository.ModalityDiagnosisRepository;
import com.smaile.health.repository.ModalityProcedureRepository;
import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.specification.ModalitySpecification;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.ModalityService;
import com.smaile.health.util.UUIDv7;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class ModalityServiceImpl implements ModalityService {

    private final ModalityRepository modalityRepository;

    private final ModalityProcedureRepository modalityProcedureRepository;

    private final ModalityDiagnosisRepository modalityDiagnosisRepository;

    private final I18nService i18nService;

    private final ModalityMapper modalityMapper;

    private final OrganizationMapper organizationMapper;

    private final OrganizationRepository organizationRepository;

    @Override
    @LogExecution
    @PreAuthorize("hasRole('IC_ADMIN')")
    @Transactional
    public UUID create(CreateModalityRequestDTO createModalityRequestDTO) {
        Optional<Modality> checkModality = modalityRepository.findByCodeAndIcOrgId(createModalityRequestDTO.getCode(),
                SecurityContextUtils.getOwnerOrganizationId());
        if (checkModality.isPresent()) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_CODE_ALREADY_EXISTS.getMessageKey())
            );
        }

        Organization organization = organizationRepository.getReferenceById(
                SecurityContextUtils.getOwnerOrganizationId());

        if (organization.getType() != OrganizationType.IC) {
            throw new SmaileRuntimeException("Organization type is not IC");
        }

        Modality modality = modalityMapper.dtoToEntity(createModalityRequestDTO);
        modality.setId(UUIDv7.generate());
        modality.setIcOrg(organization);

        Modality insertedModality = modalityRepository.save(modality);

        return insertedModality.getId();
    }

    @Override
    @LogExecution
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#modalityId)")
    @Transactional
    public void update(UUID modalityId, UpdateModalityRequestDTO updateModalityRequestDTO) {
        Modality modality = modalityRepository.findById(modalityId).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_NOT_FOUND.getMessageKey())
                )
        );

        if (!updateModalityRequestDTO.getCode().equals(modality.getCode())) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.MODALITY_CODE_IS_IMMUTABLE.getMessageKey())
            );
        }

        modality.setName(updateModalityRequestDTO.getName());
        modality.setShortName(updateModalityRequestDTO.getShortName());
        modality.setNetworkType(updateModalityRequestDTO.getNetworkType());

        modality.setAnnualLimitGlobal(updateModalityRequestDTO.getAnnualLimitGlobal());
        modality.setAnnualLimitIn(updateModalityRequestDTO.getAnnualLimitIn());
        modality.setAnnualLimitOut(updateModalityRequestDTO.getAnnualLimitOut());

        modalityRepository.save(modality);
    }

    @Override
    @LogExecution
    @Transactional
    @PreAuthorize("hasRole('IC_ADMIN') and @modalityOwnership.owns(#modalityId)")
    public DetailModalityResponse detail(UUID modalityId) {
        Modality modality = modalityRepository.findById(modalityId).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.MODALITY_NOT_FOUND.getMessageKey())
                )
        );

        DetailModalityResponse responseDTO = modalityMapper.entityToResponse(modality);
        responseDTO.setOrganizationInfo(
                organizationMapper.toDTO(modality.getIcOrg())
        );

        responseDTO.setTotalProcedures(modalityProcedureRepository.countProceduresByModalityId(modalityId));
        responseDTO.setTotalDiagnoses(modalityDiagnosisRepository.countDiagnosesByModalityId(modalityId));

        return responseDTO;
    }

    @Override
    @PreAuthorize("hasRole('IC_ADMIN')")
    public Page<ModalitySummaryResponse> query(ModalitySearchCriteria criteria, Pageable pageable) {
        Organization organization = organizationRepository.getReferenceById(
                SecurityContextUtils.getOwnerOrganizationId());

        Specification<Modality> specification = ModalitySpecification.search(criteria, organization.getId());

        Page<Modality> modalities = modalityRepository.findAll(specification, pageable);

        // get total procedures inside a modality
        List<UUID> modalityIds = modalities.map(Modality::getId).toList();
        Map<UUID, Long> countsProcedures = modalityProcedureRepository.getCountsProcedureModality(modalityIds).stream()
                .collect(
                        Collectors.toMap(TotalProcedureModality::getModalityId, TotalProcedureModality::getTotal)
                );

        // get total diagnoses inside a modality
        Map<UUID, Long> countsDiagnoses = modalityDiagnosisRepository.getCountsDiagnosisModality(modalityIds).stream()
                .collect(
                        Collectors.toMap(TotalDiagnosisModality::getModalityId, TotalDiagnosisModality::getTotal)
                );

        return modalities.map(modality -> {
            ModalitySummaryResponse responseDTO = modalityMapper.entityToSummaryResponse(modality);
            responseDTO.setTotalProcedures(countsProcedures.getOrDefault(modality.getId(), 0L));
            responseDTO.setTotalDiagnoses(countsDiagnoses.getOrDefault(modality.getId(), 0L));

            return responseDTO;
        });

    }

}
