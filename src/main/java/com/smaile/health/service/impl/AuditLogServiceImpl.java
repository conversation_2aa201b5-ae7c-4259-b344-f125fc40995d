package com.smaile.health.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.smaile.health.domain.AuditLog;
import com.smaile.health.mapper.AuditLogMapper;
import com.smaile.health.model.AuditLogDTO;
import com.smaile.health.model.AuditLogDiffLogDTO;
import com.smaile.health.model.request.Filter;
import com.smaile.health.repository.AuditLogRepository;
import com.smaile.health.repository.specification.AuditLogSpecification;
import com.smaile.health.service.AuditLogService;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@AllArgsConstructor
@Transactional
@Slf4j
public class AuditLogServiceImpl implements AuditLogService {

    private final AuditLogRepository auditLogRepository;
    private final AuditLogMapper auditLogMapper;
    private final ObjectMapper objectMapper;

    @Override
    public Page<AuditLogDTO> search(String search, List<Filter> filters, Pageable pageable) {
        Specification<AuditLog> specification = AuditLogSpecification.search(search)
                .and(AuditLogSpecification.withFilter(filters));
        Page<AuditLog> page = auditLogRepository.findAll(specification, pageable);
        return page.map(this::mapToDTO);
    }

    @Override
    public Page<AuditLogDTO> search(String search, String entity, List<Filter> filters, Pageable pageable) {
        Specification<AuditLog> specification = AuditLogSpecification.search(search)
                .and(AuditLogSpecification.withEntityName(entity))
                .and(AuditLogSpecification.withFilter(filters));
        Page<AuditLog> page = auditLogRepository.findAll(specification, pageable);
        return page.map(this::mapToDTO);
    }

    @Override
    public Page<AuditLogDTO> search(String search,
                                    String entity,
                                    UUID entityId,
                                    List<Filter> filters,
                                    Pageable pageable) {
        Specification<AuditLog> specification = AuditLogSpecification.search(search)
                .and(AuditLogSpecification.withEntityName(entity))
                .and(AuditLogSpecification.withEntityId(entityId))
                .and(AuditLogSpecification.withFilter(filters));
        Page<AuditLog> page = auditLogRepository.findAll(specification, pageable);
        return page.map(this::mapToDTO);
    }

    @Override
    public Page<AuditLogDTO> findByEntityId(UUID entityId, Pageable pageable) {
        return auditLogRepository.findByEntityId(entityId.toString(), pageable)
                .map(this::mapToDTO);
    }

    private AuditLogDTO mapToDTO(AuditLog auditLog) {
        AuditLogDTO dto = auditLogMapper.toDTO(auditLog);
        Map<String, Object> olds = convertJsonToMap(auditLog.getOldValues());
        Map<String, Object> news = convertJsonToMap(auditLog.getNewValues());
        List<AuditLogDiffLogDTO> diffs = auditLog.getChangedColumns().stream()
                .map(field -> {
                    AuditLogDiffLogDTO diff = new AuditLogDiffLogDTO();
                    diff.setField(field);
                    diff.setFrom(String.valueOf(olds.get(field)));
                    diff.setTo(String.valueOf(news.get(field)));
                    return diff;
                })
                .toList();
        dto.setDiffs(diffs);
        return dto;
    }

    private Map<String, Object> convertJsonToMap(String value) {
        if (StringUtils.isBlank(value)) {
            return Map.of();
        }
        try {
            return objectMapper.readValue(value, new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
            log.error("Failed to convert JSON to Map: {} raw {}", value, e.getMessage(), e);
            return Map.of();
        }
    }

}
