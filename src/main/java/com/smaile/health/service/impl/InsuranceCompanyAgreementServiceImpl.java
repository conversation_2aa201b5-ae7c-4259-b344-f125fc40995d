package com.smaile.health.service.impl;

import com.smaile.health.constants.ErrorCode;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.IcAgreement;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Organization;
import com.smaile.health.exception.ValidationException;
import com.smaile.health.mapper.AgreementMapper;
import com.smaile.health.mapper.IcAgreementMapper;
import com.smaile.health.mapper.InsuranceCompanyMapper;
import com.smaile.health.mapper.MedicalProviderMapper;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.InsuranceCompanyAgreementDTO;
import com.smaile.health.model.request.CreateInsuranceCompanyAgreementRequest;
import com.smaile.health.model.request.Filter;
import com.smaile.health.model.request.UpdateInsuranceCompanyAgreementRequest;
import com.smaile.health.model.response.PageResponse;
import com.smaile.health.repository.AgreementRepository;
import com.smaile.health.repository.IcAgreementRepository;
import com.smaile.health.repository.InsuranceCompanyRepository;
import com.smaile.health.repository.OrganizationRepository;
import com.smaile.health.repository.specification.IcAgreementSpecification;
import com.smaile.health.security.util.SecurityContextUtils;
import com.smaile.health.service.I18nService;
import com.smaile.health.service.InsuranceCompanyAgreementService;
import com.smaile.health.util.UUIDv7;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.UUID;

@Service
@AllArgsConstructor
public class InsuranceCompanyAgreementServiceImpl implements InsuranceCompanyAgreementService {

    private final I18nService i18nService;

    private final IcAgreementRepository icAgreementRepository;
    private final AgreementRepository agreementRepository;
    private final InsuranceCompanyRepository insuranceCompanyRepository;

    private final IcAgreementMapper icAgreementMapper;
    private final AgreementMapper agreementMapper;
    private final InsuranceCompanyMapper insuranceCompanyMapper;
    private final MedicalProviderMapper medicalProviderMapper;
    private final OrganizationRepository organizationRepository;

    @Override
    public PageResponse<InsuranceCompanyAgreementDTO> query(String search, List<Filter> filters, Pageable pageable) {
        Specification<IcAgreement> specification = IcAgreementSpecification.fetch()
                .and(IcAgreementSpecification.search(search)).and(IcAgreementSpecification.withFilter(filters));
        Page<IcAgreement> page = icAgreementRepository.findAll(specification, pageable);
        return PageResponse.of(page.map(this::toDto));
    }

    @Override
    public InsuranceCompanyAgreementDTO detail(UUID agreementId) {
        IcAgreement icAgreement = icAgreementRepository.findOne(IcAgreementSpecification.fetchOne(agreementId))
                .orElseThrow(() -> new ValidationException(
                        i18nService.getMessage(ErrorCode.IC_AGREEMENT_NOT_FOUND.getMessageKey())));
        return toDto(icAgreement);
    }

    @Override
    public InsuranceCompanyAgreementDTO create(CreateInsuranceCompanyAgreementRequest request) {
        alignInsuranceCompany(request);
        if (request.getEffectiveTime().isAfter(request.getCancellationTime())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey()));
        }
        InsuranceCompany insuranceCompany = insuranceCompanyRepository.findById(request.getInsuranceCompanyId())
                .orElseThrow(() -> new ValidationException(
                        i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey())));
        Agreement agreement = agreementRepository.findById(request.getAgreementId()).orElseThrow(
                () -> new ValidationException(i18nService.getMessage(ErrorCode.AGREEMENT_NOT_FOUND.getMessageKey())));
        boolean existedByAgreementAndInsuranceCompany = icAgreementRepository.existsByAgreementIdAndInsuranceCompanyId(
                request.getAgreementId(), request.getInsuranceCompanyId());
        if (existedByAgreementAndInsuranceCompany) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.IC_AGREEMENT_EXISTING_IC_AND_AGREEMENT_NOT_FOUND.getMessageKey()));
        }

        IcAgreement icAgreement = icAgreementMapper.toEntity(request);
        icAgreement.setId(UUIDv7.generate());
        icAgreement.setAgreement(agreement);
        icAgreement.setInsuranceCompany(insuranceCompany);
        icAgreement.setStatus(Status.INACTIVE);
        icAgreementRepository.save(icAgreement);
        return detail(icAgreement.getId());
    }

    @Override
    public InsuranceCompanyAgreementDTO update(UpdateInsuranceCompanyAgreementRequest request) {
        IcAgreement icAgreement = icAgreementRepository.findById(request.getId()).orElseThrow(
                () -> new ValidationException(
                        i18nService.getMessage(ErrorCode.IC_AGREEMENT_NOT_FOUND.getMessageKey())));
        if (request.getEffectiveTime().isAfter(request.getCancellationTime())) {
            throw new ValidationException(i18nService.getMessage(ErrorCode.TIME_RANGE_INVALID.getMessageKey()));
        }
        icAgreement.setEffectiveTime(request.getEffectiveTime());
        icAgreement.setCancellationTime(request.getCancellationTime());
        icAgreementRepository.save(icAgreement);
        return detail(icAgreement.getId());
    }

    private InsuranceCompanyAgreementDTO toDto(IcAgreement entity) {
        InsuranceCompanyAgreementDTO dto = icAgreementMapper.toDTO(entity);
        AgreementDTO agreement = agreementMapper.toDTO(entity.getAgreement());
        if (entity.getAgreement().getProvider() != null) {
            agreement.setMedicalProvider(medicalProviderMapper.toSimpleDTO(entity.getAgreement().getProvider()));
        }
        dto.setAgreement(agreement);
        dto.setInsuranceCompany(insuranceCompanyMapper.toDTO(entity.getInsuranceCompany()));
        return dto;
    }

    private void alignInsuranceCompany(CreateInsuranceCompanyAgreementRequest request) {
        Organization organization = organizationRepository.getReferenceById(
                SecurityContextUtils.getOwnerOrganizationId());
        if (!Set.of(OrganizationType.IC, OrganizationType.SUPER_SMAILE)
                .contains(organization.getType())) { // TODO: please align IC managed by TPA
            throw new ValidationException(i18nService.getMessage(ErrorCode.PERMISSION_DENIED.getMessageKey()));
        }
        if (OrganizationType.SUPER_SMAILE.equals(organization.getType()) && request.getInsuranceCompanyId() == null) {
            throw new ValidationException(
                    i18nService.getMessage(ErrorCode.INSURANCE_COMPANY_NOT_FOUND.getMessageKey()));
        }

        if (OrganizationType.IC.equals(organization.getType())) {
            request.setInsuranceCompanyId(organization.getId());
        }
    }

}
