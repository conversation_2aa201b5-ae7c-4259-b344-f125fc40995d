package com.smaile.health.service;

import com.smaile.health.model.request.CreateModalityProcedureRequestDTO;
import com.smaile.health.model.response.ModalityProcedureResponseDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface ModalityProcedureService {

    UUID create(CreateModalityProcedureRequestDTO createModalityProcedureRequestDTO);

    void delete(UUID id);

    Page<ModalityProcedureResponseDTO> query(String keyword, UUID modalityId, Pageable pageable);
}
