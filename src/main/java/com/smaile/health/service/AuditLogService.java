package com.smaile.health.service;

import com.smaile.health.model.AuditLogDTO;
import com.smaile.health.model.request.Filter;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface AuditLogService {
    Page<AuditLogDTO> search(String search, List<Filter> filters, Pageable pageable);
    Page<AuditLogDTO> search(String search, String entity, List<Filter> filters, Pageable pageable);
    Page<AuditLogDTO> search(String search, String entity, UUID entityId, List<Filter> filters, Pageable pageable);
    Page<AuditLogDTO> findByEntityId(UUID entityId, Pageable pageable);
}
