package com.smaile.health.service;

import com.smaile.health.model.request.CreateModalityDiagnosisRequestDTO;
import com.smaile.health.model.response.ModalityDiagnosisResponseDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface ModalityDiagnosisService {
    UUID create(CreateModalityDiagnosisRequestDTO dto);

    void delete(UUID id);

    Page<ModalityDiagnosisResponseDTO> query(String keyword, UUID modalityId, Pageable pageable);
}
