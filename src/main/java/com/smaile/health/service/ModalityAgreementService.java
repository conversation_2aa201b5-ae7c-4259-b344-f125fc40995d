package com.smaile.health.service;

import com.smaile.health.model.response.ModalityAgreementResponseDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface ModalityAgreementService {

    void linkAgreement(UUID modalityId, UUID agreementId);
    void unlinkAgreement(UUID modalityId, UUID agreementId);
    Page<ModalityAgreementResponseDTO> query(String search, UUID modalityId, Pageable pageable);
}
