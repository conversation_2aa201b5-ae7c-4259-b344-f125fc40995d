package com.smaile.health.service;

import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.Status;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.UsersSummaryDTO;
import com.smaile.health.model.response.UserResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

public interface UserService {

    UserResponse getAboutMe();

    SmaileUserCredential createWithPasswordGenerated(UUID orgId,
                                                     UserDTO userDTO,
                                                     boolean notification,
                                                     boolean hasTempPassword);

    UUID createProfessionalUser(UUID orgId, UserDTO userDTO, String password);

    void updateUser(UUID id, UserDTO userDTO);

    UserDTO get(UUID id);

    boolean isAvailableToCreate(UserDTO userDto);

    UsersSummaryDTO queryUsersSummaryData();

    UUID resetPassword(UUID orgId, UserDTO userDTO);

    /**
     * Get user name by UUID
     *
     * @param userId User UUID
     * @return User full name or UUID string if user not found
     */
    String getUserName(UUID userId);

    void activate(UUID orgId, UUID userId);

    void deactivate(UUID orgId, UUID userId);

    void updateLastLogin(UUID userKeycloakId, long timestamp);

    UUID getOrgIdByUserId(UUID userId);

    Page<UserDTO> queryUserByCriteria(UUID organizationId,
                                      String partialNameOrEmail,
                                      RoleEnum roleCode,
                                      Status status,
                                      Pageable pageable);

}