package com.smaile.health.service;

import com.smaile.health.domain.Organization;
import com.smaile.health.model.EmailResponse;
import com.smaile.health.model.SmaileUserCredential;
import com.smaile.health.model.UserDTO;
import com.smaile.health.model.request.EmailRequest;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface EmailService {

    /**
     * Send a single email
     *
     * @param emailRequest the email request
     * @return EmailResponse with the result
     */
    EmailResponse sendEmail(EmailRequest emailRequest);

    /**
     * Send a single email asynchronously
     *
     * @param emailRequest the email request
     * @return CompletableFuture with EmailResponse
     */
    CompletableFuture<EmailResponse> sendEmailAsync(EmailRequest emailRequest);

    /**
     * Send multiple emails asynchronously
     *
     * @param emailRequests list of email requests
     * @return list of CompletableFuture with EmailResponse
     */
    List<CompletableFuture<EmailResponse>> sendBulkEmailsAsync(List<EmailRequest> emailRequests);

    /**
     * Send email using a template
     *
     * @param templateId   the template ID
     * @param templateData the template data
     * @param to           recipients
     * @param subject      email subject
     * @return EmailResponse with the result
     */
    EmailResponse sendTemplatedEmail(String templateId,
                                     java.util.Map<String, Object> templateData,
                                     List<String> to,
                                     String subject);

    void sendOrganizationOnboardNotification(UserDTO userDTO,
                                             Organization organization,
                                             SmaileUserCredential userCredential);

}
