package com.smaile.health.service;

import com.smaile.health.model.UserDTO;

import java.util.Optional;
import java.util.UUID;

public interface IdentityProvider {

    String createUser(UserDTO userDto);

    String createUserWithPassword(UserDTO userDto, String password, boolean hasTempPassword);

    Optional<UUID> findByEmailAndUsername(String email, String username);

    void resetPassword(String userKeycloakId, String newPassword);

    void enableUser(String userKeycloakId);

    void disableUser(String userKeycloakId);

}
