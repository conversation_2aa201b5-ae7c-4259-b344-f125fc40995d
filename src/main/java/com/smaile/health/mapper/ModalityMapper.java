package com.smaile.health.mapper;

import com.smaile.health.domain.Modality;
import com.smaile.health.model.ModalityDTO;
import com.smaile.health.model.request.CreateModalityRequestDTO;
import com.smaile.health.model.response.DetailModalityResponse;
import com.smaile.health.model.response.ModalitySummaryResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModalityMapper {

    Modality dtoToEntity(CreateModalityRequestDTO dto);

    DetailModalityResponse entityToResponse(Modality modality);

    ModalityDTO entityToDto(Modality modality);

    ModalitySummaryResponse entityToSummaryResponse(Modality modality);
}
