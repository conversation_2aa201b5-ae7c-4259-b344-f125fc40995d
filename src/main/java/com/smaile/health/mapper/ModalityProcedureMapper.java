package com.smaile.health.mapper;

import com.smaile.health.domain.ModalityProcedure;
import com.smaile.health.model.ModalityProcedureLiteDTO;
import com.smaile.health.model.request.CreateModalityProcedureRequestDTO;
import com.smaile.health.model.response.ModalityProcedureResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {DiagnosisMapper.class, ModalityDiagnosisMapper.class})
public interface ModalityProcedureMapper {

    ModalityProcedure dtoToEntity(CreateModalityProcedureRequestDTO dto);

    ModalityProcedureLiteDTO entityToDTO(ModalityProcedure modalityProcedure);

    ModalityProcedureResponseDTO entityToDTOResponse(ModalityProcedure modalityProcedure);

}
