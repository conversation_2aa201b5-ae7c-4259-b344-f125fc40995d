package com.smaile.health.mapper;

import com.smaile.health.domain.ModalityProcedureCoverage;
import com.smaile.health.model.request.CoverageDTO;
import com.smaile.health.model.request.CoverageRequestDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ModalityProcedureCoverageMapper {

    ModalityProcedureCoverage requestDtoToEntity(CoverageRequestDTO coverageRequestDTO);

    CoverageDTO entityToDTO(ModalityProcedureCoverage modalityProcedureCoverage);
}
