package com.smaile.health.mapper;

import com.smaile.health.domain.Professional;
import com.smaile.health.model.ProfessionalDTO;
import com.smaile.health.model.ProfessionalSimpleInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ProfessionalMapper {

    ProfessionalDTO toDTO(Professional professional);

    ProfessionalSimpleInfoDTO toSimpleInfoDTO(Professional professional);

    Professional toEntity(ProfessionalDTO professionalDTO);

}