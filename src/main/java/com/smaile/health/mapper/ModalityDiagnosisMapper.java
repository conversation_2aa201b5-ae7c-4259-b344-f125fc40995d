package com.smaile.health.mapper;

import com.smaile.health.domain.ModalityDiagnosis;
import com.smaile.health.model.ModalityDiagnosisDTO;
import com.smaile.health.model.request.CreateModalityDiagnosisRequestDTO;
import com.smaile.health.model.response.ModalityDiagnosisResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {ModalityDiagnosisMapper.class})
public interface ModalityDiagnosisMapper {

    ModalityDiagnosis dtoToEntity(CreateModalityDiagnosisRequestDTO dto);

    ModalityDiagnosisDTO entityToDTO(ModalityDiagnosis entity);

    ModalityDiagnosisResponseDTO entityToResponseDTO(ModalityDiagnosis entity);

}
