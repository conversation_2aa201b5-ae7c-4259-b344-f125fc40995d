package com.smaile.health.mapper;

import com.smaile.health.domain.Speciality;
import com.smaile.health.model.request.CreateSpecialityRequestDTO;
import com.smaile.health.model.SpecialityDTO;
import com.smaile.health.model.SpecialitySummaryDTO;
import com.smaile.health.model.response.SpecialityResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class SpecialityMapper extends BaseMapper {

    public abstract SpecialityDTO toDTO(Speciality speciality);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    public abstract Speciality toEntity(CreateSpecialityRequestDTO requestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "dateCreated", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastUpdated", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    public abstract void updateEntityFromDTO(SpecialityDTO dto, @MappingTarget Speciality speciality);

    public abstract SpecialitySummaryDTO toSummaryDTO(Speciality speciality);

    public abstract SpecialityResponseDTO toResponseDTO(Speciality speciality);

}
