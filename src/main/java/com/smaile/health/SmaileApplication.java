package com.smaile.health;

import com.smaile.health.config.AppConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = ErrorMvcAutoConfiguration.class)
@EnableScheduling
@EnableConfigurationProperties({ AppConfig.class })
public class SmaileApplication {

    public static void main(final String[] args) {
        SpringApplication.run(SmaileApplication.class, args);
    }

}
