package com.smaile.health.model;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.OffsetDateTime;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@Builder
public class ProfessionalSimpleInfoDTO {

    private String id;
    private String fullProfessionalName;
    private String primaryLicenseId;
    private String primaryPracticeMarket;
    private String country;
    private String marketSegment;
    private String name;
    private String code;
    private OrganizationType type;
    private String registrationNumber;
    private OrganizationStatus status;
    private String contactPhone;
    private String contactEmail;
    private String address;
    private OffsetDateTime dateCreated;
    private String createdBy;
    private OffsetDateTime lastUpdated;
    private String updatedBy;

}
