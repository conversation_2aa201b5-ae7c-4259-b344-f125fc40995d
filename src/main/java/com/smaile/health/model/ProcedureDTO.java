package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.DiagnosisFrequency;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Schema(description = "Procedure Data Transfer Object")
public class ProcedureDTO extends AuditDTO {

    private UUID id;

    @NotBlank
    private String code;

    @NotNull
    private DiagnosisFrequency frequency;

    @NotBlank
    private String description;

    @JsonProperty("age_group")
    @NotNull
    private AgeGroup ageGroup;

    @JsonProperty("market_cost")
    @NotNull
    private BigDecimal marketCost;

    @JsonProperty("mouth_part_requirements")
    private List<String> mouthPartRequirements;

    @JsonProperty("related_diagnoses")
    private List<DiagnosisSummaryDTO> relatedDiagnoses;

    @JsonProperty("effective_date")
    @NotNull
    private LocalDate effectiveDate;

    @JsonProperty("cancellation_date")
    private LocalDate cancellationDate;

    @JsonProperty("specialties")
    private List<SpecialitySummaryDTO> specialties;

    @JsonProperty("proofs")
    private List<ProofSummaryDTO> proofs;

    @JsonProperty("is_active")
    private Boolean isActive;
}