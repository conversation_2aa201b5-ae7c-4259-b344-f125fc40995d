package com.smaile.health.model.request;

import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.Frequency;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@RequiredArgsConstructor
public class UpdateModalityProcedureRequestDTO {

    @NotNull
    @Size(max = 2, min = 1)
    private List<CoverageRequestDTO> coverages;

    private List<String> overrideDiagnosisCodes;

    private Frequency overrideFrequency;

    private BigDecimal overrideMarketCostAmount;

    private AgeGroup ageGroup;

    @NotNull
    private LocalDate effectiveDate;

    private LocalDate cancellationDate;

}
