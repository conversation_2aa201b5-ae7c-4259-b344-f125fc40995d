package com.smaile.health.model.request;

import com.smaile.health.constants.InsuredGenderEnum;
import com.smaile.health.constants.LegalIdTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class LinkModalityAgreementRequest {

    @NotNull
    private UUID modalityId;

    @NotNull
    private UUID agreementId;

}
