package com.smaile.health.model.request;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.NetworkType;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class CoverageRequestDTO {

    @NotNull
    private NetworkType networkType;

    private BigDecimal fixedAmount;

    @Min(0)
    @Max(100)
    private Integer percentage;

    private BigDecimal deductibleAmount;

    @JsonCreator
    public CoverageRequestDTO(
            @JsonProperty("network_type") NetworkType networkType,
            @JsonProperty("fixed_amount") BigDecimal fixedAmount,
            @JsonProperty("percentage") Integer percentage,
            @JsonProperty("deductible_mount") BigDecimal deductibleAmount
    ) {
        if (networkType.equals(NetworkType.ALL)) {
            throw new IllegalArgumentException("Network Type is either IN or OUT");
        }

        if (fixedAmount == null && percentage == null || fixedAmount != null && percentage != null) {
            throw new IllegalArgumentException("Either Fixed Amount OR Percentage must be defined");
        }

        if (percentage != null) {
            if (percentage < 0 || percentage > 100) {
                throw new IllegalArgumentException("Percentage must be between 0 and 100");
            }
        }

        this.networkType = networkType;
        this.fixedAmount = fixedAmount;
        this.percentage = percentage;
        this.deductibleAmount = deductibleAmount;
    }
}
