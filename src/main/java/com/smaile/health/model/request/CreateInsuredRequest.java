package com.smaile.health.model.request;

import com.smaile.health.constants.InsuredGenderEnum;
import com.smaile.health.constants.LegalIdTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class CreateInsuredRequest {
    @NotNull
    private UUID modalityId;

    @NotBlank
    private String insuredCode;

    private LocalDate firstEffectiveDate;

    @NotNull
    private LocalDate coverageBeginDate;

    @NotNull
    private LocalDate coverageEndDate;

    private LegalIdTypeEnum legalIdType;

    private String legalIdValue;

    private InsuredGenderEnum gender;

    private LocalDate dateOfBirth;

    private String firstName;

    private String lastName;
}
