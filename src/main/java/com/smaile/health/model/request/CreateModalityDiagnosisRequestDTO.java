package com.smaile.health.model.request;

import com.smaile.health.constants.Frequency;
import com.smaile.health.constants.NetworkType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
public class CreateModalityDiagnosisRequestDTO {

    @NotNull
    private UUID modalityId;

    @NotNull
    private String diagnosisCode;

    @NotNull
    private NetworkType networkType;

    private Frequency overrideFrequency;

    private BigDecimal overrideMarketCostAmount;

    @NotNull
    private LocalDate effectiveDate;

    private LocalDate cancellationDate;
}
