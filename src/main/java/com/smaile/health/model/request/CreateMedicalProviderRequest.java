package com.smaile.health.model.request;

import com.smaile.health.constants.MedicalProviderTypeEnum;
import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.SmaileConstant;
import com.smaile.health.model.FileUploadDTO;
import com.smaile.health.model.MedicalProviderLicense;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateMedicalProviderRequest {

    @NotBlank
    private String name;

    @NotNull
    private MedicalProviderTypeEnum providerType;

    @NotBlank
    private String registrationNumber;

    @NotBlank
    private String market;

    @NotBlank
    private String country;

    @NotNull
    private OrganizationStatus status;

    private String contactPhone;

    @NotBlank
    @Size(max = 255)
    @Pattern(regexp = SmaileConstant.EMAIL_REGEX, message = "Invalid email format")
    private String contactEmail;

    private String address;

    @NotEmpty
    private List<String> specialities;

    private List<MedicalProviderLicense> licenses;

    @NotNull
    @Valid
    private CreateOrgAdminRequest adminUserRequest;

    private List<FileUploadDTO> attachmentFiles;
}
