package com.smaile.health.model;

import com.smaile.health.constants.DiagnosisFrequency;
import com.smaile.health.constants.MouthPartType;
import com.smaile.health.model.response.SpecialityResponseDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class DiagnosisLiteDTO {

    private UUID id;

    private String code;

    private DiagnosisFrequency frequency;

    private String description;

    private MouthPartType mouthPartType;

    private String mouthPartId;

    private BigDecimal marketCost;

    private BigDecimal knowledgeCost;

    private List<SpecialityResponseDTO> specialties;

    private LocalDate effectiveDate;

    private LocalDate cancellationDate;
}
