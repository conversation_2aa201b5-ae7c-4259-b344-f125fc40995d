package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
public class AgreementProcedureDTO {

    @NotNull
    private UUID procedureId;

    @NotNull
    private BigDecimal cost;

    private ProcedureLiteDTO procedureInfo;

}
