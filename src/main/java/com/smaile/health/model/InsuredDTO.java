package com.smaile.health.model;

import com.smaile.health.constants.InsuredGenderEnum;
import com.smaile.health.constants.InsuredStatusEnum;
import com.smaile.health.constants.LegalIdTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class InsuredDTO extends AuditDTO {
    private UUID id;
    private UUID modalityId;
    private UUID icOrgId;
    private ModalityDTO modality;
    private InsuranceCompanyDTO icOrg;
    private String insuredCode;
    private LocalDate firstEffectiveDate;
    private LocalDate coverageBeginDate;
    private LocalDate coverageEndDate;
    private InsuredStatusEnum status;
    private LocalDate statusDate;
    private String statusReason;
    private LegalIdTypeEnum legalIdType;
    private String legalIdValue;
    private InsuredGenderEnum gender;
    private LocalDate dateOfBirth;
    private String firstName;
    private String lastName;
    private Boolean isDeleted;
}
