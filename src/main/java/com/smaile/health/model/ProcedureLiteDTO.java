package com.smaile.health.model;

import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.DiagnosisFrequency;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
public class ProcedureLiteDTO {

    private UUID id;

    private String code;

    private DiagnosisFrequency frequency;

    private String description;

    private AgeGroup ageGroup;

    private BigDecimal marketCost;

    private List<String> mouthPartRequirements;

    private LocalDate effectiveDate;

    private LocalDate cancellationDate;
}