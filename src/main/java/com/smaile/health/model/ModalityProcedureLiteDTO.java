package com.smaile.health.model;

import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.Frequency;
import com.smaile.health.constants.NetworkType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
public class ModalityProcedureLiteDTO {

    private UUID id;

    private String procedureCode;

    private NetworkType networkType;

    private Frequency overrideFrequency;

    private BigDecimal overrideMarketCostAmount;

    private AgeGroup ageGroup;

    private OffsetDateTime dateCreated;

    private String createdBy;

    private OffsetDateTime lastUpdated;

    private String updatedBy;

    private LocalDate effectiveDate;

    private LocalDate cancellationDate;
}
