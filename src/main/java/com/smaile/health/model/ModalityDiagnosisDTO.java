package com.smaile.health.model;

import com.smaile.health.constants.Frequency;
import com.smaile.health.constants.ModalityDiagnosisType;
import com.smaile.health.constants.NetworkType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
public class ModalityDiagnosisDTO {

    private UUID id;

    private String DiagnosisCode;

    private NetworkType networkType;

    private ModalityDiagnosisType type;

    private Frequency overrideFrequency;

    private BigDecimal overrideMarketCostAmount;

    private LocalDate effectiveDate;

    private LocalDate cancellationDate;

}
