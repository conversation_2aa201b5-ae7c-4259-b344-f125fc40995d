package com.smaile.health.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.smaile.health.constants.OrganizationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.OffsetDateTime;
import java.util.UUID;

/**
 * Contains only essential fields.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Insurance Company Search Result Data Transfer Object")
public class InsuranceCompanySearchDTO {

    @Schema(
            description = "Unique identifier of the insurance company",
            example = "018f1234-5678-9abc-def0-123456789abc",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("id")
    private UUID id;

    @Schema(
            description = "Name of the insurance company",
            example = "ABC Insurance Co.",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Internal code for the insurance company",
            example = "ABC001",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("code")
    private String code;

    @Schema(
            description = "Official registration number issued by regulatory authorities",
            example = "REG123456",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("registration_number")
    private String registrationNumber;

    @Schema(
            description = "Current status of the insurance company",
            example = "ACTIVE",
            allowableValues = { "ACTIVE", "DRAFT", "ARCHIVED" },
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("status")
    private OrganizationStatus status;

    @Schema(
            description = "Contact phone number for the insurance company",
            example = "******-0123",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("contact_phone")
    private String contactPhone;

    @Schema(
            description = "Contact email address for the insurance company",
            example = "<EMAIL>",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("contact_email")
    private String contactEmail;

    @Schema(
            description = "Physical address of the insurance company",
            example = "123 Main St, New York, NY 10001",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("address")
    private String address;

    @Schema(
            description = "Market/region where the insurance company operates",
            example = "US",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("market")
    private String market;

    @Schema(
            description = "Country where the insurance company is registered",
            example = "United States",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("country")
    private String country;

    @Schema(
            description = "Total number of IC_ADMIN users associated with this insurance company",
            example = "3",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("total_admin")
    private Integer totalAdmin;

    @Schema(
            description = "Parent organization information",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("parent")
    private ParentOrganizationDTO parent;

    @Schema(
            description = "Timestamp when the insurance company was created",
            example = "2024-01-15T10:30:00Z",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("date_created")
    private OffsetDateTime dateCreated;

    @Schema(
            description = "User who created the insurance company",
            example = "<EMAIL>",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("created_by")
    private String createdBy;

    @Schema(
            description = "Timestamp when the insurance company was last updated",
            example = "2024-01-15T10:30:00Z",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("last_updated")
    private OffsetDateTime lastUpdated;

    @Schema(
            description = "User who last updated the insurance company",
            example = "<EMAIL>",
            accessMode = Schema.AccessMode.READ_ONLY
    )
    @JsonProperty("updated_by")
    private String updatedBy;

}
