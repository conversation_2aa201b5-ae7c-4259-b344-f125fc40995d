package com.smaile.health.model.response;

import com.smaile.health.constants.Frequency;
import com.smaile.health.constants.ModalityDiagnosisType;
import com.smaile.health.constants.NetworkType;
import com.smaile.health.model.DiagnosisLiteDTO;
import com.smaile.health.model.ModalityProcedureLiteDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Data
public class ModalityDiagnosisResponseDTO {

    private UUID id;

    private String diagnosisCode;

    private DiagnosisLiteDTO diagnosis;

    private ModalityDiagnosisType type;

    private List<ModalityProcedureLiteDTO>  modalityProcedures;

    private NetworkType networkType;

    private Frequency overrideFrequency;

    private BigDecimal overrideMarketCostAmount;

    private LocalDate effectiveDate;

    private LocalDate cancellationDate;

    private OffsetDateTime dateCreated;

    private String createdBy;

    private OffsetDateTime lastUpdated;

    private String updatedBy;
}
