package com.smaile.health.model.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.time.OffsetDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class GeneratePresignedUrlResponse {
    private String url;
    private String objectLocation;
    private OffsetDateTime expiryTime;
}