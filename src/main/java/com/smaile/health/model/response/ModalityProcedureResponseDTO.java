package com.smaile.health.model.response;

import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.Frequency;
import com.smaile.health.model.ModalityDiagnosisDTO;
import com.smaile.health.model.ProcedureLiteDTO;
import com.smaile.health.model.request.CoverageDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Set;
import java.util.UUID;

@Data
public class ModalityProcedureResponseDTO {


    private UUID id;

    private ProcedureLiteDTO procedure;

    private String procedureCode;

    private Set<CoverageDTO> coverages;

    private Set<ModalityDiagnosisDTO> modalityDiagnoses;

    private Frequency overrideFrequency;

    private BigDecimal overrideMarketCostAmount;

    private AgeGroup ageGroup;

    private LocalDate effectiveDate;

    private LocalDate cancellationDate;

    private OffsetDateTime dateCreated;

    private String createdBy;

    private OffsetDateTime lastUpdated;

    private String updatedBy;
}
