package com.smaile.health.model.response;

import com.smaile.health.constants.AgeGroup;
import com.smaile.health.constants.Frequency;
import com.smaile.health.constants.NetworkType;
import com.smaile.health.model.AgreementDTO;
import com.smaile.health.model.ModalityDiagnosisDTO;
import com.smaile.health.model.ProcedureLiteDTO;
import com.smaile.health.model.request.CoverageDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Set;
import java.util.UUID;

@Data
public class ModalityAgreementResponseDTO {

    private AgreementDTO agreement;

    private OffsetDateTime dateCreated;

    private String createdBy;

    private OffsetDateTime lastUpdated;

    private String updatedBy;
}
