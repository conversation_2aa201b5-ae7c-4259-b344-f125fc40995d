package com.smaile.health.model.response;

import com.smaile.health.constants.NetworkType;
import com.smaile.health.model.OrganizationDTO;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

@Data
public class DetailModalityResponse {
    @NotNull
    private UUID id;

    @NotNull
    private String code;

    @NotNull
    private String name;

    @NotNull
    private String shortName;

    @NotNull
    private NetworkType networkType;

    @NotNull
    private OrganizationDTO organizationInfo;

    private BigDecimal annualLimitGlobal;

    private BigDecimal annualLimitIn;

    private BigDecimal annualLimitOut;

    private long totalProcedures;

    private long totalDiagnoses;

    @NotNull
    private OffsetDateTime dateCreated;

    @NotNull
    private String createdBy;

    @NotNull
    private OffsetDateTime lastUpdated;

    @NotNull
    private String updatedBy;
}
