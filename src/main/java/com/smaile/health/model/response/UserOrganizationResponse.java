package com.smaile.health.model.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.smaile.health.constants.OrganizationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.UUID;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserOrganizationResponse {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserOrganizationResponse parent;
    private UUID id;
    private String name;
    private OrganizationType type;
    private String registrationNumber;
    private final String country = "N/A"; //TODO: clarify with Doan
    private final String market = "N/A"; //TODO: clarify with <PERSON><PERSON>
}
