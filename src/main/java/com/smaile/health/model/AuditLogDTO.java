package com.smaile.health.model;

import com.smaile.health.constants.AuditOperation;
import com.smaile.health.constants.EntityType;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class AuditLogDTO {

    private LocalDateTime createdAt;
    private EntityType entityName;
    private UUID entityId;
    private AuditOperation operation;
    private String createdBy;
    private List<String> changedColumns;
    private List<AuditLogDiffLogDTO> diffs;

}
