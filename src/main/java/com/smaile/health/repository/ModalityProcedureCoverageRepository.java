package com.smaile.health.repository;

import com.smaile.health.domain.ModalityProcedureCoverage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Set;
import java.util.UUID;

@Repository
public interface ModalityProcedureCoverageRepository extends JpaRepository<ModalityProcedureCoverage, UUID> {

    void deleteByIdIn(Set<UUID> ids);
}
