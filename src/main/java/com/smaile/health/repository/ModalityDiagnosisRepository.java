package com.smaile.health.repository;

import com.smaile.health.domain.ModalityDiagnosis;
import com.smaile.health.model.ModalityDiagnosisId;
import com.smaile.health.model.TotalDiagnosisModality;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Repository
public interface ModalityDiagnosisRepository extends JpaRepository<ModalityDiagnosis, UUID>, JpaSpecificationExecutor<ModalityDiagnosis> {

    @Query(value = """
            SELECT md
            FROM ModalityDiagnosis md
            JOIN FETCH Diagnosis d ON md.diagnosis.id = d.id
            WHERE md.id = :modalityDiagnosisId
            """)
    Optional<ModalityDiagnosis> findModalityDiagnosis(UUID modalityDiagnosisId);

    @Modifying
    @Query("""
    DELETE FROM ModalityDiagnosis d
    WHERE d IN :modalityDiagnoses
      AND d.modalityProcedures IS EMPTY
    """)
    void deleteOrphansDiagnosis(Set<ModalityDiagnosis> modalityDiagnoses);


    Optional<ModalityDiagnosis> findByModalityIdAndDiagnosisId(UUID modalityId, UUID diagnosisId);

    @Query("""
            SELECT md.id as id, md.diagnosis.id as diagnosisId
            FROM ModalityDiagnosis md
            WHERE md.modality.id = :modalityId
              AND (
                LOWER(md.diagnosis.code) LIKE LOWER(CONCAT('%', :keyword, '%'))
                OR LOWER(md.diagnosis.description) LIKE LOWER(CONCAT('%', :keyword, '%'))
              )
            """)
    Page<ModalityDiagnosisId> searchToFindIds(String keyword, UUID modalityId, Pageable pageable);

    @EntityGraph(attributePaths = {"modalityProcedures"})
    @Query("SELECT md FROM ModalityDiagnosis md WHERE md.id IN :modalityDiagnosisIds")
    List<ModalityDiagnosis> loadModalityDiagnosisWithProcedure(List<UUID> modalityDiagnosisIds);


    @Query(
            value = """
                    SELECT modality_id, COUNT(diagnosis_id) as total
                    FROM modality_diagnosis
                    WHERE modality_id IN :modalityIds
                    GROUP BY modality_id
                    """
            , nativeQuery = true)
    List<TotalDiagnosisModality> getCountsDiagnosisModality(List<UUID> modalityIds);

    long countDiagnosesByModalityId(UUID modalityId);
}
