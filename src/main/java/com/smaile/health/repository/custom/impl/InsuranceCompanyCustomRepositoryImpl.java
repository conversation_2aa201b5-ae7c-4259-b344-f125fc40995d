package com.smaile.health.repository.custom.impl;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.Status;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.domain.Organization;
import com.smaile.health.domain.UserOrganization;
import com.smaile.health.model.InsuranceCompanySearchDTO;
import com.smaile.health.model.ParentOrganizationDTO;
import com.smaile.health.repository.custom.InsuranceCompanyCustomRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Custom repository implementation for InsuranceCompany entity.
 */
@Repository
@Slf4j
public class InsuranceCompanyCustomRepositoryImpl implements InsuranceCompanyCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<InsuranceCompanySearchDTO> searchWithConditions(
            String market,
            String status,
            String name,
            UUID parentOrgId,
            Pageable pageable) {

        log.debug("Executing search with projection - Market: {}, Status: {}, Name: {}, ParentOrgId: {}",
                market, status, name, parentOrgId);

        CriteriaBuilder cb = createCriteriaBuilder();

        CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
        Root<InsuranceCompany> root = query.from(InsuranceCompany.class);
        Join<InsuranceCompany, Organization> parentJoin = root.join("parent", JoinType.LEFT);

        query.multiselect(
                root.get("id"),
                root.get("name"),
                root.get("code"),
                root.get("registrationNumber"),
                root.get("status"),
                root.get("contactPhone"),
                root.get("contactEmail"),
                root.get("address"),
                root.get("market"),
                root.get("country"),
                parentJoin.get("id"),
                parentJoin.get("name"),
                root.get("dateCreated"),
                root.get("createdBy"),
                root.get("lastUpdated"),
                root.get("updatedBy")
        );

        List<Predicate> predicates = buildPredicates(cb, root, market, status, name, parentOrgId);
        query.where(predicates.toArray(new Predicate[0]));

        if (pageable.getSort().isSorted()) {
            pageable.getSort().forEach(order -> {
                if (order.isAscending()) {
                    query.orderBy(cb.asc(root.get(order.getProperty())));
                } else {
                    query.orderBy(cb.desc(root.get(order.getProperty())));
                }
            });
        } else {
            query.orderBy(cb.desc(root.get("dateCreated")));
        }

        TypedQuery<Object[]> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Object[]> rawResults = typedQuery.getResultList();

        List<UUID> organizationIds = rawResults.stream()
                .map(row -> (UUID) row[0])
                .toList();

        Map<UUID, Integer> userCounts = getBatchUserCounts(organizationIds);

        List<InsuranceCompanySearchDTO> results = rawResults.stream()
                .map(row -> mapToInsuranceCompanySearchDTO(row, userCounts))
                .toList();

        long total = getSearchCount(market, status, name, parentOrgId);

        log.debug("Search completed - Found {} results out of {} total", results.size(), total);
        return new PageImpl<>(results, pageable, total);
    }

    private CriteriaBuilder createCriteriaBuilder() {
        return entityManager.getCriteriaBuilder();
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<InsuranceCompany> root,
                                            String market, String status, String name, UUID parentOrgId) {
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(cb.equal(root.get("type"), OrganizationType.IC));

        if (StringUtils.hasText(market)) {
            predicates.add(cb.equal(root.get("market"), market));
        }

        if (StringUtils.hasText(status)) {
            try {
                OrganizationStatus statusEnum = OrganizationStatus.fromString(status);
                predicates.add(cb.equal(root.get("status"), statusEnum));
            } catch (IllegalArgumentException e) {
                predicates.add(cb.equal(root.get("status"), OrganizationStatus.ACTIVE));
            }
        } else {
            predicates.add(cb.notEqual(root.get("status"), OrganizationStatus.INACTIVE));
        }

        if (StringUtils.hasText(name)) {
            predicates.add(cb.like(
                    cb.lower(cb.coalesce(root.get("name"), "")),
                    "%" + name.toLowerCase() + "%"
            ));
        }

        if (parentOrgId != null) {
            predicates.add(cb.equal(root.get("parent").get("id"), parentOrgId));
        }

        return predicates;
    }

    private long getSearchCount(String market, String status, String name, UUID parentOrgId) {
        CriteriaBuilder cb = createCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<InsuranceCompany> root = countQuery.from(InsuranceCompany.class);

        countQuery.select(cb.count(root));

        List<Predicate> predicates = buildPredicates(cb, root, market, status, name, parentOrgId);
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private InsuranceCompanySearchDTO mapToInsuranceCompanySearchDTO(Object[] row, Map<UUID, Integer> userCounts) {
        UUID id = (UUID) row[0];
        String name = (String) row[1];
        String code = (String) row[2];
        String registrationNumber = (String) row[3];
        OrganizationStatus status = (OrganizationStatus) row[4];
        String contactPhone = (String) row[5];
        String contactEmail = (String) row[6];
        String address = (String) row[7];
        String market = (String) row[8];
        String country = (String) row[9];
        UUID parentId = (UUID) row[10];
        String parentName = (String) row[11];
        OffsetDateTime dateCreated = (OffsetDateTime) row[12];
        String createdBy = (String) row[13];
        OffsetDateTime lastUpdated = (OffsetDateTime) row[14];
        String updatedBy = (String) row[15];

        Integer totalAdmin = userCounts.getOrDefault(id, 0);

        ParentOrganizationDTO parent = null;
        if (parentId != null) {
            parent = new ParentOrganizationDTO(parentId, parentName);
        }

        return InsuranceCompanySearchDTO.builder()
                .id(id)
                .name(name)
                .code(code)
                .registrationNumber(registrationNumber)
                .status(status)
                .contactPhone(contactPhone)
                .contactEmail(contactEmail)
                .address(address)
                .market(market)
                .country(country)
                .totalAdmin(totalAdmin)
                .parent(parent)
                .dateCreated(dateCreated)
                .createdBy(createdBy)
                .lastUpdated(lastUpdated)
                .updatedBy(updatedBy)
                .build();
    }

    /**
     * Get user counts for multiple organizations in a single query to avoid N+1 problem
     */
    private Map<UUID, Integer> getBatchUserCounts(List<UUID> organizationIds) {
        if (organizationIds.isEmpty()) {
            return new HashMap<>();
        }

        CriteriaBuilder cb = createCriteriaBuilder();
        CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
        Root<UserOrganization> root = query.from(UserOrganization.class);

        query.multiselect(
                root.get("organization").get("id"),
                cb.count(root)
        );

        query.where(
                root.get("organization").get("id").in(organizationIds),
                cb.equal(root.get("status"), Status.ACTIVE)
        );

        query.groupBy(root.get("organization").get("id"));

        List<Object[]> results = entityManager.createQuery(query).getResultList();

        Map<UUID, Integer> userCounts = new HashMap<>();
        for (Object[] result : results) {
            UUID orgId = (UUID) result[0];
            Long count = (Long) result[1];
            userCounts.put(orgId, count != null ? count.intValue() : 0);
        }

        for (UUID orgId : organizationIds) {
            userCounts.putIfAbsent(orgId, 0);
        }

        return userCounts;
    }

}
