package com.smaile.health.repository.custom;

import com.smaile.health.model.InsuranceCompanySearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

/**
 * Custom repository interface for InsuranceCompany data.
 */
public interface InsuranceCompanyCustomRepository {

    /**
     * Search for insurance companies with efficient data fetching.
     *
     * @param market      the market filter (optional)
     * @param status      the status filter (optional)
     * @param name        the name filter for partial matching (optional)
     * @param parentOrgId the parent organization ID for filtering (optional)
     * @param pageable    pagination parameters
     * @return Page of InsuranceCompanySearchDTO with efficient data loading
     */
    Page<InsuranceCompanySearchDTO> searchWithConditions(
            String market,
            String status,
            String name,
            UUID parentOrgId,
            Pageable pageable
    );

}
