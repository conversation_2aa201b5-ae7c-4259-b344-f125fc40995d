package com.smaile.health.repository;

import com.smaile.health.domain.ModalityAgreements;
import com.smaile.health.domain.id.ModalityAgreementId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface ModalityAgreementRepository extends JpaRepository<ModalityAgreements, ModalityAgreementId>, JpaSpecificationExecutor<ModalityAgreements> {

    boolean existsById_ModalityIdAndId_AgreementId(UUID modalityId, UUID agreementId);
}
