package com.smaile.health.repository.impl;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.constants.OrganizationType;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.projection.OrganizationInfo;
import com.smaile.health.domain.projection.PermissionInfo;
import com.smaile.health.domain.projection.RoleInfo;
import com.smaile.health.domain.projection.UserInfo;
import com.smaile.health.repository.UserRepositoryCustom;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Tuple;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class UserRepositoryCustomImpl implements UserRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public UserInfo findUserProjectionByKeycloakId(String keycloakId) {
        String jpql = """
                SELECT
                    u.id AS userId,
                    u.keycloakId AS keycloakId,
                    u.username AS username,
                    u.email AS email,
                    u.fullName AS fullName,
                    u.phone AS phone,
                    u.status AS userStatus,
                    uo.organization.parent.id AS parentId,
                    uo.organization.id AS organizationId,
                    uo.organization.name AS organizationName,
                    uo.organization.type AS organizationType,
                    uo.organization.status AS organizationStatus,
                    ur.role.id AS roleId,
                    ur.role.name AS roleName,
                    ur.role.code AS roleCode,
                    p.id AS permissionId,
                    p.name AS permissionName,
                    p.resource AS resource,
                    p.subResource AS subResource,
                    p.action AS action,
                    p.description AS permissionDescription,
                    CASE
                        WHEN u.organization.id = uo.organization.id THEN 'owner'
                        ELSE 'delegate'
                    END AS userType
                FROM User u
                LEFT JOIN UserOrganization uo ON u.id = uo.user.id
                LEFT JOIN UserRole ur ON ur.userOrganization.id = uo.id
                LEFT JOIN ur.role.permissions p
                WHERE u.keycloakId = :keycloakId
                """;

        List<Tuple> results = entityManager.createQuery(jpql, Tuple.class)
                .setParameter("keycloakId", keycloakId)
                .getResultList();

        if (results.isEmpty()) {
            return null;
        }

        return mapToUserProjectionWithAliases(results);
    }

    private UserInfo mapToUserProjectionWithAliases(List<Tuple> results) {
        Map<UUID, List<Tuple>> orgGroups = results.stream()
                .collect(Collectors.groupingBy(tuple -> tuple.get("organizationId", UUID.class)));

        Tuple firstRow = results.get(0);
        UUID userId = firstRow.get("userId", UUID.class);
        UUID keycloakId = UUID.fromString(firstRow.get("keycloakId", String.class));
        String username = firstRow.get("username", String.class);
        String email = firstRow.get("email", String.class);
        String fullName = firstRow.get("fullName", String.class);
        String phone = firstRow.get("phone", String.class);
        UserStatus userStatus = firstRow.get("userStatus", UserStatus.class);

        List<OrganizationInfo> delegateOrgs = new ArrayList<>();
        OrganizationInfo ownerOrg = null;

        for (Map.Entry<UUID, List<Tuple>> orgEntry : orgGroups.entrySet()) {
            List<Tuple> orgRows = orgEntry.getValue();
            OrganizationInfo org = buildOrganizationProjectionWithAliases(orgRows);

            // Check userType from the CASE statement
            String userType = orgRows.get(0).get("userType", String.class);

            if ("owner".equals(userType)) {
                ownerOrg = org;
            } else {
                delegateOrgs.add(org);
            }
        }

        final OrganizationInfo finalOwnerOrg = ownerOrg;
        final List<OrganizationInfo> finalDelegateOrgs = delegateOrgs;

        return new UserInfo() {
            @Override
            public UUID getId() {
                return userId;
            }

            @Override
            public UUID getKeycloakId() {
                return keycloakId;
            }

            public String getUsername() {
                return username;
            }

            @Override
            public String getEmail() {
                return email;
            }

            @Override
            public String getFullName() {
                return fullName;
            }

            @Override
            public String getPhone() {
                return phone;
            }

            @Override
            public UserStatus getStatus() {
                return userStatus;
            }

            @Override
            public OrganizationInfo getOwnerOrganization() {
                return finalOwnerOrg;
            }

            @Override
            public List<OrganizationInfo> getDelegateOrganizations() {
                return finalDelegateOrgs;
            }
        };
    }

    private OrganizationInfo buildOrganizationProjectionWithAliases(List<Tuple> orgRows) {
        Tuple firstRow = orgRows.get(0);
        UUID parentId = firstRow.get("parentId", UUID.class);
        UUID orgId = firstRow.get("organizationId", UUID.class);
        String orgName = firstRow.get("organizationName", String.class);
        OrganizationType orgType = firstRow.get("organizationType", OrganizationType.class);
        OrganizationStatus orgStatus = firstRow.get("organizationStatus", OrganizationStatus.class);

        Map<UUID, List<Tuple>> roleGroups = orgRows.stream()
                .collect(Collectors.groupingBy(tuple -> tuple.get("roleId", UUID.class)));

        RoleInfo role = null;
        if (!roleGroups.isEmpty()) {
            Map.Entry<UUID, List<Tuple>> roleEntry = roleGroups.entrySet().iterator().next();
            role = buildRoleProjectionWithAliases(roleEntry.getValue());
        }

        final RoleInfo finalRole = role;

        return new OrganizationInfo() {
            @Override
            public UUID getParentId() {
                return parentId;
            }

            @Override
            public UUID getId() {
                return orgId;
            }

            @Override
            public String getName() {
                return orgName;
            }

            @Override
            public OrganizationType getType() {
                return orgType;
            }

            @Override
            public OrganizationStatus getStatus() {
                return orgStatus;
            }

            @Override
            public RoleInfo getRole() {
                return finalRole;
            }
        };
    }

    private RoleInfo buildRoleProjectionWithAliases(List<Tuple> roleRows) {
        Tuple firstRow = roleRows.get(0);
        String roleName = firstRow.get("roleName", String.class);
        String roleCode = firstRow.get("roleCode", String.class);

        List<PermissionInfo> permissions = roleRows.stream()
                .map(this::buildPermissionProjectionWithAliases)
                .distinct()
                .toList();

        return new RoleInfo() {
            @Override
            public String getName() {
                return roleName;
            }

            @Override
            public String getCode() {
                return roleCode;
            }

            @Override
            public List<PermissionInfo> getPermissions() {
                return permissions;
            }
        };
    }

    private PermissionInfo buildPermissionProjectionWithAliases(Tuple tuple) {
        UUID permissionId = tuple.get("permissionId", UUID.class);
        String permissionName = tuple.get("permissionName", String.class);
        String resource = tuple.get("resource", String.class);
        String subResource = tuple.get("subResource", String.class);
        String action = tuple.get("action", String.class);
        String description = tuple.get("permissionDescription", String.class);

        return new PermissionInfo() {
            @Override
            public UUID getId() {
                return permissionId;
            }

            @Override
            public String getName() {
                return permissionName;
            }

            @Override
            public String getResource() {
                return resource;
            }

            @Override
            public String getSubResource() {
                return subResource;
            }

            @Override
            public String getAction() {
                return action;
            }

            @Override
            public String getDescription() {
                return description;
            }

            @Override
            public int hashCode() {
                return Objects.hash(getId());
            }

            @Override
            public boolean equals(Object o) {
                if (this == o) {
                    return true;
                }
                if (!(o instanceof PermissionInfo that)) {
                    return false;
                }
                return Objects.equals(getId(), that.getId());
            }
        };
    }

}
