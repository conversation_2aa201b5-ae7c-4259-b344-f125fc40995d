package com.smaile.health.repository;

import com.smaile.health.domain.Modality;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ModalityRepository extends JpaRepository<Modality, UUID>, JpaSpecificationExecutor<Modality> {

    @Query(value = """
            SELECT * FROM modalities WHERE code = :code AND ic_org_id = :icOrgId
            """, nativeQuery = true)
    Optional<Modality> findByCodeAndIcOrgId(String code, UUID icOrgId);

    @Query(value = """
            SELECT EXISTS (SELECT 1 FROM modalities WHERE id = :id AND ic_org_id = :icOrgId)
            """, nativeQuery = true)
    boolean existsByIdAndIcOrgId(UUID id, UUID icOrgId);
}
