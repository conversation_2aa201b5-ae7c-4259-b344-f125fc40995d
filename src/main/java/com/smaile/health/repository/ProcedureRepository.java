package com.smaile.health.repository;

import com.smaile.health.domain.Procedure;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ProcedureRepository extends JpaRepository<Procedure, UUID>, JpaSpecificationExecutor<Procedure> {

    boolean existsByCodeIgnoreCase(String code);

    boolean existsByCodeIgnoreCaseAndIdNot(String code, UUID id);

    @EntityGraph(attributePaths = {"relatedDiagnoses"})
    Optional<Procedure> findByCodeAndIsActiveTrue(String code);

    @EntityGraph(attributePaths = {"relatedDiagnoses", "specialties", "proofs"})
    Optional<Procedure> findById(UUID id);
}
