package com.smaile.health.repository;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.Professional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
public interface ProfessionalRepository
        extends JpaRepository<Professional, UUID>, JpaSpecificationExecutor<Professional> {

    boolean existsByPrimaryLicenseId(String primaryLicenseId);

    boolean existsByUsername(String username);

    boolean existsByContactEmail(String contactEmail);

    int countByStatusAndParentId(OrganizationStatus status, UUID parentId);

    int countByParentId(UUID parentId);

    @Query("""
            SELECT COUNT(DISTINCT s.id)
            FROM Professional p
            JOIN p.specialities s
            """)
    long countDistinctSpecialities();

    @Query("""
        SELECT p
        FROM Professional p
        INNER JOIN ProviderProfessional pp ON p.id = pp.professionalId
        WHERE pp.providerId IN :medicalProviderIds AND pp.status = 'ACTIVE' AND p.status = 'ACTIVE'
        """)
    List<Professional> findAllProfessionalLinkedWithMedicalProvider(List<UUID> medicalProviderIds);

    @Query("""
        SELECT pp.providerId, COUNT(*)
        FROM Professional p
        INNER JOIN ProviderProfessional pp ON p.id = pp.professionalId
        WHERE pp.providerId IN :medicalProviderIds AND pp.status = 'ACTIVE' AND p.status = 'ACTIVE'
        GROUP BY pp.providerId
        """)
    List<Object[]> countByProviderRaw(List<UUID> medicalProviderIds);

    default Map<UUID, Long> countByProvider(List<UUID> medicalProviderIds) {
        List<Object[]> raw = countByProviderRaw(medicalProviderIds);
        return raw.stream()
                .collect(Collectors.toMap(
                        row -> (UUID) row[0],
                        row -> (Long) row[1]
                ));
    }
}
