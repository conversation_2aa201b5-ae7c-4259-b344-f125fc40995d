package com.smaile.health.repository;

import com.smaile.health.domain.OrgAttachment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Repository
public interface OrgAttachmentRepository extends JpaRepository<OrgAttachment, UUID> {

    @Query("""
            SELECT oa
            FROM OrgAttachment oa
            WHERE oa.organization.id = :orgId
                AND oa.isDeleted = false
            """)
    List<OrgAttachment> findByOrgIdAndNotDeleted(UUID orgId);
}
