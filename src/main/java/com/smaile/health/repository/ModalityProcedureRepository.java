package com.smaile.health.repository;

import com.smaile.health.domain.ModalityProcedure;
import com.smaile.health.model.TotalProcedureModality;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ModalityProcedureRepository extends JpaRepository<ModalityProcedure, UUID>, JpaSpecificationExecutor<ModalityProcedure> {

    @Query(value = """
            SELECT md
            FROM ModalityProcedure md
            JOIN FETCH Procedure d ON md.procedure.id = d.id
            WHERE md.id = :modalityProcedureId
            """)
    Optional<ModalityProcedure> findModalityProcedure(UUID modalityProcedureId);

    Optional<ModalityProcedure> findByModalityIdAndProcedureId(UUID modalityId, UUID procedureId);

    @Query("""
                SELECT mp.id
                FROM ModalityProcedure mp
                JOIN mp.procedure p
                WHERE mp.modality.id = :modalityId
                  AND (
                    LOWER(p.code) LIKE LOWER(CONCAT('%', :keyword, '%'))
                    OR LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%'))
                  )
            """)
    Page<UUID> searchToFindIds(String keyword, UUID modalityId, Pageable pageable);

    @EntityGraph(attributePaths = {"procedure", "modalityDiagnoses", "coverages"})
    @Query("SELECT mp FROM ModalityProcedure mp WHERE mp.id IN :modalityProcedureIds")
    List<ModalityProcedure> loadModalityProcedureSummary(List<UUID> modalityProcedureIds);


    @Query(
            value = """
                    SELECT modality_id, COUNT(procedure_id) as total
                    FROM modality_procedures
                    WHERE modality_id IN :modalityIds
                    GROUP BY modality_id
                    """
            , nativeQuery = true)
    List<TotalProcedureModality> getCountsProcedureModality(List<UUID> modalityIds);

    long countProceduresByModalityId(UUID modalityId);
}
