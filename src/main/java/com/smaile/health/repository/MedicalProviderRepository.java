package com.smaile.health.repository;

import com.smaile.health.domain.MedicalProvider;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.UUID;

public interface MedicalProviderRepository extends JpaRepository<MedicalProvider, UUID>, JpaSpecificationExecutor<MedicalProvider> {
    @Query("""
            SELECT mp
            FROM MedicalProvider mp
                INNER JOIN ProviderProfessional pp ON mp.id = pp.providerId
                INNER JOIN Professional pro ON pp.professionalId = pro.id
            WHERE pro.id = :professionalId
                AND pp.status = 'ACTIVE'
                AND pro.status = 'ACTIVE'
                AND mp.status = 'ACTIVE'
            """)
    Page<MedicalProvider> findByLinkedProfessional(UUID professionalId, Pageable pageable);

    boolean existsByIdAndParentId(UUID id, UUID parentId);
}
