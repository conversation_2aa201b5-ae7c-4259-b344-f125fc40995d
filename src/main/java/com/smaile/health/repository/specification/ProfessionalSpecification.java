package com.smaile.health.repository.specification;

import com.smaile.health.domain.Professional;
import com.smaile.health.domain.Speciality;
import com.smaile.health.model.request.Filter;
import jakarta.persistence.criteria.*;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ProfessionalSpecification {

    public static Specification<Professional> searchProfessional(String search) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();

            if (StringUtils.hasText(search)) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(root.get("fullProfessionalName"), "")),
                        "%" + search.toLowerCase() + "%"
                ));
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<Professional> withFilter(List<Filter> filters) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            for (Filter filter : filters) {
                if ("status".equals(filter.getField())) {
                    predicates.add(criteriaBuilder.equal(
                            root.get("status"),
                            filter.getValue()
                    ));
                } else if ("country".equals(filter.getField())) {
                    predicates.add(criteriaBuilder.equal(
                            criteriaBuilder.lower(criteriaBuilder.coalesce(root.get("country"), "")),
                            filter.getValue().toLowerCase()
                    ));
                } else if ("marketSegment".equals(filter.getField())) {
                    predicates.add(criteriaBuilder.equal(
                            criteriaBuilder.lower(criteriaBuilder.coalesce(root.get("marketSegment"), "")),
                            filter.getValue().toLowerCase()
                    ));
                } else if ("speciality".equals(filter.getField())) {
                    // Join to specialities
                    Join<Professional, Speciality> specialityJoin = root.join("specialities", JoinType.LEFT);
                    predicates.add(specialityJoin.get("id").in(List.of(UUID.fromString(filter.getValue()))));
                }
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Predicate equal(Root<?> root, CriteriaBuilder criteriaBuilder, Expression<?> field, String value) {
        return criteriaBuilder.equal(field, value);
    }

}
