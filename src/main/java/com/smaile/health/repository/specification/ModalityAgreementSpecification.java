package com.smaile.health.repository.specification;

import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.Modality;
import com.smaile.health.domain.ModalityAgreements;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.UUID;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ModalityAgreementSpecification {

    public static Specification<ModalityAgreements> fetch() {
        return (root, query, criteriaBuilder) -> {
            if (query.getResultType() != Long.class && query.getResultType() != long.class) {
                Fetch<Object, Object> agreementFetch = root.fetch(ModalityAgreements.Fields.AGREEMENT, JoinType.LEFT);
                // Second layer (nested fetch)
                agreementFetch.fetch(Agreement.Fields.PROVIDER, JoinType.LEFT);
            }
            return criteriaBuilder.conjunction();
        };
    }
    public static Specification<ModalityAgreements> search(String search) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            if (StringUtils.hasText(search)) {
                var agreement = root.get(ModalityAgreements.Fields.AGREEMENT);
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(agreement.get(Agreement.Fields.CONTRACT_ID), "")),
                        "%" + search.toLowerCase() + "%"
                ));
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(criteriaBuilder.coalesce(agreement.get(Agreement.Fields.SORT_DESCRIPTION), "")),
                        "%" + search.toLowerCase() + "%"
                ));
            }
            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<ModalityAgreements> withModalityId(UUID modalityId) {
        return (root, criteriaQuery, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            if (modalityId != null) {
                var modality = root.get(ModalityAgreements.Fields.MODALITY);
                predicates.add(
                        criteriaBuilder.equal(
                                modality.get(Modality.Fields.ID),
                                modalityId
                        )
                );
            }
            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
