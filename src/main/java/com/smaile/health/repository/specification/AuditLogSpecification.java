package com.smaile.health.repository.specification;

import com.smaile.health.domain.Agreement;
import com.smaile.health.domain.AuditLog;
import com.smaile.health.domain.Organization;
import com.smaile.health.model.request.Filter;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class AuditLogSpecification {

    public static Specification<AuditLog> search(String search) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            // TODO: implement search across relevant fields
            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<AuditLog> withFilter(List<Filter> filters) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            for (Filter filter : filters) {
                if (AuditLog.Fields.OPERATION.equals(filter.getField())) {
                    predicates.add(
                            criteriaBuilder.equal(
                                root.get(AuditLog.Fields.OPERATION),
                                filter.getValue()
                            )
                    );
                }
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<AuditLog> withEntityName(String entityName) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            if (StringUtils.hasText(entityName)) {
                predicates.add(criteriaBuilder.equal(root.get(AuditLog.Fields.ENTITY_NAME), entityName));
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Specification<AuditLog> withEntityId(UUID entityId) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            if (entityId != null) {
                predicates.add(criteriaBuilder.equal(root.get(AuditLog.Fields.ENTITY_ID), entityId));
            }

            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
