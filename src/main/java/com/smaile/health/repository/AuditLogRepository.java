package com.smaile.health.repository;

import com.smaile.health.domain.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.OffsetDateTime;
import java.util.List;

/**
 * JPA Repository for AuditLog entity.
 * Provides database access methods for audit log operations.
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-09-07
 */
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> , JpaSpecificationExecutor<AuditLog> {

    /**
     * Find audit logs by entity name and ID within a date range.
     * Useful for retrieving audit history for a specific entity.
     */
    @Query("SELECT a FROM AuditLog a WHERE a.entityName = :entityName AND a.entityId = :entityId " +
           "AND a.createdAt BETWEEN :startDate AND :endDate ORDER BY a.createdAt DESC")
    List<AuditLog> findByEntityAndDateRange(@Param("entityName") String entityName,
                                           @Param("entityId") String entityId,
                                           @Param("startDate") OffsetDateTime startDate,
                                           @Param("endDate") OffsetDateTime endDate);

    /**
     * Find audit logs by user within a date range.
     * Useful for user activity tracking.
     */
    @Query("SELECT a FROM AuditLog a WHERE a.createdBy = :createdBy " +
           "AND a.createdAt BETWEEN :startDate AND :endDate ORDER BY a.createdAt DESC")
    List<AuditLog> findByUserAndDateRange(@Param("createdBy") String createdBy,
                                         @Param("startDate") OffsetDateTime startDate,
                                         @Param("endDate") OffsetDateTime endDate);

    /**
     * Count audit logs by entity name within a date range.
     * Useful for audit statistics.
     */
    @Query("SELECT COUNT(a) FROM AuditLog a WHERE a.entityName = :entityName " +
           "AND a.createdAt BETWEEN :startDate AND :endDate")
    Long countByEntityNameAndDateRange(@Param("entityName") String entityName,
                                      @Param("startDate") OffsetDateTime startDate,
                                      @Param("endDate") OffsetDateTime endDate);

    Page<AuditLog> findByEntityId(String entityId, Pageable pageable);
}
