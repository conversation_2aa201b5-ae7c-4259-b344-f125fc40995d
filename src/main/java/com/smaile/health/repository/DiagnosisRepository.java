package com.smaile.health.repository;

import com.smaile.health.domain.Diagnosis;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface DiagnosisRepository extends JpaRepository<Diagnosis, UUID>, JpaSpecificationExecutor<Diagnosis> {

    Optional<Diagnosis> findByCodeIgnoreCaseAndIsActiveTrue(String code);

    Optional<Diagnosis> findByCodeIgnoreCaseAndIdNotAndIsActiveTrue(String code, UUID id);

    List<Diagnosis> findByIsActiveTrueOrderByCode();

    Optional<Diagnosis> findByCodeAndIsActiveTrue(String code);

    @EntityGraph(attributePaths = "specialties")
    @Query("""
            SELECT d FROM Diagnosis d WHERE d.id IN :diagnosisIds
            """)
    List<Diagnosis> loadWithSpeciality(List<UUID> diagnosisIds);
}
