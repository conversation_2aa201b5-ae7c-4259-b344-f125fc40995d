package com.smaile.health.repository;

import com.smaile.health.domain.User;
import com.smaile.health.domain.projection.UsersSummaryProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;
import java.util.UUID;

public interface UserRepository
        extends JpaRepository<User, UUID>, JpaSpecificationExecutor<User>, UserRepositoryCustom {

    Optional<User> findOneByEmail(String email);

    @Query(value = """
            SELECT COUNT(*) AS total_users,
                    COUNT(*) FILTER(WHERE u.status = 'ACTIVE') AS active_users,
                    COUNT(*) FILTER (WHERE u.status = 'INACTIVE') AS inactive_users,
                    COUNT(DISTINCT uo.organization.id) AS total_organizations
            FROM User u
                INNER JOIN UserOrganization uo ON u.id = uo.user.id
                INNER JOIN Organization o ON uo.organization.id = o.id
            WHERE uo.organization.id = COALESCE(:actorOrgId, uo.organization.id)
                OR o.parent.id = COALESCE(:actorOrgId, o.parent.id)
            """)
    UsersSummaryProjection getUsersSummary(UUID actorOrgId);

    Optional<User> findByKeycloakId(String keycloakId);

}
