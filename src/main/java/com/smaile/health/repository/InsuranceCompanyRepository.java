package com.smaile.health.repository;

import com.smaile.health.constants.OrganizationStatus;
import com.smaile.health.domain.InsuranceCompany;
import com.smaile.health.repository.custom.InsuranceCompanyCustomRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface InsuranceCompanyRepository
        extends JpaRepository<InsuranceCompany, UUID>, JpaSpecificationExecutor<InsuranceCompany>,
        InsuranceCompanyCustomRepository {

    Optional<InsuranceCompany> findByNameAndIdNot(String name, UUID excludeId);

    Optional<InsuranceCompany> findByNameAndStatusNot(String name, OrganizationStatus status);

    Optional<InsuranceCompany> findByIdAndStatusNot(UUID id, OrganizationStatus status);

    List<InsuranceCompany> findByStatus(OrganizationStatus status);

    List<InsuranceCompany> findByStatusAndParentIdIn(OrganizationStatus status, List<UUID> parentIds);

}
