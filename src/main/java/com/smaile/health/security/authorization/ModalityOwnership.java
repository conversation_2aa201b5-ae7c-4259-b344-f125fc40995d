package com.smaile.health.security.authorization;

import com.smaile.health.domain.ModalityDiagnosis;
import com.smaile.health.domain.ModalityProcedure;
import com.smaile.health.repository.ModalityDiagnosisRepository;
import com.smaile.health.repository.ModalityProcedureRepository;
import com.smaile.health.repository.ModalityRepository;
import com.smaile.health.security.util.SecurityContextUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.UUID;


@Component("modalityOwnership")
@RequiredArgsConstructor
public class ModalityOwnership {

    private final ModalityRepository modalityRepository;

    private final ModalityProcedureRepository modalityProcedureRepository;

    private final ModalityDiagnosisRepository modalityDiagnosisRepository;

    public boolean owns(UUID modalityId) {
        return modalityRepository.existsByIdAndIcOrgId(modalityId, SecurityContextUtils.getOwnerOrganizationId());
    }

    public boolean ownsProcedure(UUID modalityProcedureId) {

        ModalityProcedure modalityProcedure = modalityProcedureRepository.findModalityProcedure(modalityProcedureId)
                .orElse(null);

        if (modalityProcedure == null) {
            return false;
        }

        return owns(modalityProcedure.getModality().getId());
    }

    public boolean ownsDiagnosis(UUID modalityDiagnosisId) {
        ModalityDiagnosis modalityDiagnosis = modalityDiagnosisRepository.findModalityDiagnosis(modalityDiagnosisId)
                .orElse(null);

        if (modalityDiagnosis == null) {
            return false;
        }

        return owns(modalityDiagnosis.getModality().getId());
    }
}
