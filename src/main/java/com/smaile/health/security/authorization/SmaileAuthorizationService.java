package com.smaile.health.security.authorization;

import com.smaile.health.security.authentication.SmaileAuthenticationToken;
import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.util.SecurityContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * Service responsible for authorization decisions in the SMAILE Health platform.
 * I'm considering to use this approach or with permission evaluator
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Service("smaileAuthorizationService")
@RequiredArgsConstructor
@Slf4j
// TODO: considering to use this approach or with permission evaluator. Because this can be use as a logic and customization
public class SmaileAuthorizationService {

    private static boolean isInvalidAuthentication(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated()) {
            log.debug("Invalid authentication object provided");
            return true;
        }
        return false;
    }

    /**
     * Checks if the authenticated user has inherited permission of the parent organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @return true if user has inherited permission of the parent organization, false otherwise
     */
    public boolean isInheritPermissionOrganization(Authentication authentication,
                                                   UUID organizationId,
                                                   String permission) {
        if (isInvalidAuthentication(authentication)) {
            return false;
        }
        return SecurityContextUtils.getOwnerOrganizationContexts()
                .hasPermission(permission) && SecurityContextUtils.getOrganizationContexts()
                .containsKey(organizationId);
    }

    /**
     * Checks if the authenticated user has a specific permission in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param permission     The permission to verify
     * @return true if user has the permission, false otherwise
     */
    public boolean hasPermissionInOrganization(Authentication authentication, UUID organizationId, String permission) {
        if (isInvalidInput(authentication, organizationId, permission)) {
            return false;
        }

        try {
            OrganizationPermissionContext context = SecurityContextUtils.getOrganizationContext(organizationId);
            return hasPermissionContext(authentication, permission, context);
        } catch (Exception e) {
            log.warn("Error checking permission for user {} in organization {}: {}", authentication.getName(),
                    organizationId, e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the authenticated user has a specific permission in an organization.
     *
     * @param authentication The user's authentication object
     * @param permission     The permission to verify
     * @return true if user has the permission, false otherwise
     */
    public boolean hasPermissionInOwnerOrganization(Authentication authentication, String permission) {
        if (isInvalidInput(authentication, permission)) {
            return false;
        }

        UUID organizationId = SecurityContextUtils.getOwnerOrganizationId();
        try {
            OrganizationPermissionContext context = SecurityContextUtils.getOwnerOrganizationContexts();
            return hasPermissionContext(authentication, permission, context);
        } catch (Exception e) {
            log.warn("Error checking permission for user {} in owner organization {}: {}", authentication.getName(),
                    organizationId, e.getMessage());
            return false;
        }
    }

    private static boolean hasPermissionContext(Authentication authentication,
                                                String permission,
                                                OrganizationPermissionContext context) {
        // TODO: Need to implement permission evaluation to implies wildcards or owner
        boolean hasPermission = context.getPermissions()
                .stream()
                .anyMatch(ctx -> ctx.contains(permission));

        log.debug("Permission check: user={}, org={}, permission={}, result={}", authentication.getName(),
                context.getOrganizationId(), permission, hasPermission);

        return hasPermission;
    }

    /**
     * Checks if the authenticated user has a specific role in an organization.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID to check
     * @param role           The role to verify
     * @return true if user has the role, false otherwise
     */
    public boolean hasRoleInOrganization(Authentication authentication, UUID organizationId, String role) {
        if (isInvalidInput(authentication, organizationId, role)) {
            return false;
        }

        try {
            OrganizationPermissionContext context = SecurityContextUtils.getOrganizationContext(organizationId);
            boolean hasRole = context.getRole().equals(role);

            log.debug("Role check: user={}, org={}, role={}, result={}", authentication.getName(), organizationId, role,
                    hasRole);

            return hasRole;
        } catch (Exception e) {
            log.warn("Error checking role for user {} in organization {}: {}", authentication.getName(), organizationId,
                    e.getMessage());
            return false;
        }
    }

    /**
     * Checks if the authenticated user has a specific role in owner organization.
     *
     * @param roles The roles to verify
     * @return true if user has the role, false otherwise
     */
    public boolean hasRole(String... roles) {
        SmaileAuthenticationToken authentication = SecurityContextUtils.getCurrentAuthentication();
        UUID organizationId = SecurityContextUtils.getOwnerOrganizationId();
        for (String role : roles) {
            if (hasRoleInOrganization(authentication, organizationId, role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validates the input parameters for permission/role checks.
     *
     * @param authentication The user's authentication object
     * @param organizationId The organization ID
     * @param roles          The permission/role roles to check
     * @return true if any input is invalid, false otherwise
     */
    private boolean isInvalidInput(Authentication authentication, UUID organizationId, String... roles) {
        if (isInvalidAuthentication(authentication)) {
            return true;
        }

        if (isInvalidOrganizationId(organizationId)) {
            return true;
        }

        return isInvalidRoles(roles);
    }

    /**
     * Validates the input parameters for permission/role checks.
     *
     * @param authentication The user's authentication object
     * @param roles          The permission/role roles to check
     * @return true if any input is invalid, false otherwise
     */
    private boolean isInvalidInput(Authentication authentication, String... roles) {
        if (isInvalidAuthentication(authentication)) {
            return true;
        }

        return isInvalidRoles(roles);
    }

    /**
     * Validates the input parameters for permission/role checks.
     *
     * @param organizationId The organization ID
     * @return true if any input is invalid, false otherwise
     */
    private boolean isInvalidOrganizationId(UUID organizationId) {
        if (organizationId == null) {
            log.debug("Organization ID cannot be null");
            return true;
        }
        return false;
    }

    /**
     * Validates the input parameters for permission/role checks.
     *
     * @param roles The permission/role roles to check
     * @return true if any input is invalid, false otherwise
     */
    private boolean isInvalidRoles(String... roles) {
        if (roles == null || roles.length == 0) {
            log.debug("Roles cannot be null or empty");
            return true;
        }
        for (String role : roles) {
            if (role == null || role.trim().isEmpty()) {
                log.debug("Roles cannot be null or empty");
                return true;
            }
        }
        return false;
    }

}
