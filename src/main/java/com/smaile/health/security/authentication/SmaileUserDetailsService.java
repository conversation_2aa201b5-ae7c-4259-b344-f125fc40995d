package com.smaile.health.security.authentication;

import com.smaile.health.domain.projection.OrganizationInfo;
import com.smaile.health.domain.projection.UserInfo;
import com.smaile.health.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * UserDetailsService implementation for SMAILE Health platform.
 * This service loads user details from the database for authentication purposes.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Service
@Transactional(readOnly = true)
@Slf4j
@RequiredArgsConstructor
public class SmaileUserDetailsService implements UserDetailsService {

    private final UserRepository userRepository;

    @Override
    public UserDetails loadUserByUsername(String keycloakId) throws UsernameNotFoundException {
        return loadUserByKeycloakId(keycloakId);
    }

    private SmaileUserDetails loadUserByKeycloakId(String keycloakId) throws UsernameNotFoundException {
        log.debug("Loading user details for Keycloak ID: {}", keycloakId);

        if (keycloakId == null || keycloakId.trim().isEmpty()) {
            throw new UsernameNotFoundException("Keycloak user ID cannot be empty");
        }

        UserInfo user = userRepository.findUserProjectionByKeycloakId(keycloakId.trim());
        if (user == null) {
            log.warn("User not found or disabled for Keycloak ID: {}", keycloakId);
            throw new UsernameNotFoundException("User not found or disabled: " + keycloakId);
        }

        OrganizationInfo ownerOrganization = user.getOwnerOrganization();
        if (ownerOrganization == null) {
            log.warn("User not found or disabled for Keycloak ID: {}", keycloakId);
            throw new UsernameNotFoundException("User not found or disabled: " + keycloakId);
        }
        log.debug("Successfully loaded user: (Keycloak ID: {}) with role: {}", user.getKeycloakId(),
                ownerOrganization.getRole().getCode());

        return new SmaileUserDetails(user);
    }

}