package com.smaile.health.security.authentication;

import com.smaile.health.constants.RoleEnum;
import com.smaile.health.constants.UserStatus;
import com.smaile.health.domain.projection.UserInfo;
import com.smaile.health.security.context.OrganizationPermissionContext;
import com.smaile.health.security.context.SecurityContextBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

/**
 * Custom UserDetails implementation for Keycloak users.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/08/25
 */
@Getter
@Slf4j
public class SmaileUserDetails implements UserDetails {

    private final transient UserInfo user;
    private final Map<UUID, OrganizationPermissionContext> organizationPermissionContexts;
    private final Collection<GrantedAuthority> authorities;

    public SmaileUserDetails(UserInfo user) {
        this.user = user;
        this.organizationPermissionContexts = SecurityContextBuilder.buildOrganizationPermissionContexts(user);
        String role = user.getOwnerOrganization().getRole().getCode();
        this.authorities = List.of(new SimpleGrantedAuthority("ROLE_" + role));
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return null;
    }

    @Override
    public String getUsername() {
        return user.getUsername();
    }

    @Override
    public boolean isEnabled() {
        return UserStatus.ACTIVE.equals(user.getStatus());
    }

    public UUID getKeycloakId() {
        return user.getKeycloakId();
    }

    public String getEmail() {
        return user.getEmail();
    }

    @Override
    public int hashCode() {
        return Objects.hash(user.getKeycloakId());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SmaileUserDetails that = (SmaileUserDetails) o;
        return Objects.equals(user.getKeycloakId(), that.user.getKeycloakId());
    }

    public boolean isSuperAdmin() {
        return RoleEnum.SUPER_SMAILE_ADMIN.name()
                .equals(this.user.getOwnerOrganization().getRole().getCode());
    }

    public UUID getOwnerOrganizationId() {
        return user.getOwnerOrganization().getId();
    }

}