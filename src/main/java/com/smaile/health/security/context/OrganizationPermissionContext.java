package com.smaile.health.security.context;

import com.smaile.health.constants.OrganizationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;
import java.util.UUID;

/**
 * Represents the permission context for a user within a specific organization.
 * Contains all permissions available to the user in that organization.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationPermissionContext implements Serializable {

    private UUID organizationId;
    private String organizationName;
    private OrganizationType organizationType;
    private Set<String> permissions;
    private String role;
    private boolean isOwner;

    /**
     * Check if user has a specific permission in this organization
     */
    public boolean hasPermission(String permission) {
        return permissions != null && permissions.contains(permission);
    }

    /**
     * Check if user has a specific role in this organization
     */
    public boolean hasRole(String role) {
        return this.role.equals(role);
    }

}
