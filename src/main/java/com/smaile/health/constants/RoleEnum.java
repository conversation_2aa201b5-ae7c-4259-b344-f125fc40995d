package com.smaile.health.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum RoleEnum {
    SUPER_SMAILE_ADMIN(OrganizationType.SUPER_SMAILE, RoleScope.ADMIN, "Smaile system Admin"),
    PROFESSIONAL(OrganizationType.PROFESSIONAL, RoleScope.ADMIN, "Professional"),

    IC_ADMIN(OrganizationType.IC, RoleScope.ADMIN, "Insurance Company Admin"),
    IC_TPA_ADMIN(OrganizationType.IC_TPA, RoleScope.ADMIN, "Insurance Company TPA Admin"),
    IC_TPA_MP_ADMIN(OrganizationType.IC_TPA_MP, RoleScope.ADMIN, "Insurance Company TPA Medical Provider Admin"),
    IC_MP_ADMIN(OrganizationType.IC_MP, RoleScope.ADMIN, "Insurance Company Medical Provider Admin"),

    SMAILE_TPA_ADMIN(OrganizationType.SMAILE_TPA, RoleScope.ADMIN, "Admin of TPA under <PERSON><PERSON><PERSON>"),
    <PERSON>AIL<PERSON>_MP_ADMIN(OrganizationType.SMAILE_MP, RoleScope.ADMIN, "Admin of Medical Provider under Smaile"),
    SMAILE_TPA_MP_ADMIN(OrganizationType.SMAILE_TPA_MP, RoleScope.ADMIN, "Smaile TPA Medical Provider Admin");

    private OrganizationType orgType;
    private RoleScope scope;
    private String description;

    private static final Map<String, RoleEnum> mappings = new HashMap<>();

    static {
        for (RoleEnum role : RoleEnum.values()) {
            mappings.put(role.name(), role);
        }
    }

    public static RoleEnum resolve(String roleName) {
        return (roleName == null ? null : mappings.get(roleName));
    }

    public boolean matches(String roleName) {
        return (this == resolve(roleName));
    }
}
