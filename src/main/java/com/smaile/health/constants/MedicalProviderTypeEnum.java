package com.smaile.health.constants;

import java.util.HashMap;
import java.util.Map;

public enum MedicalProviderTypeEnum {
    INDIVIDUAL,
    LEGAL_ENTITY;

    private static final Map<String, MedicalProviderTypeEnum> mapping = new HashMap<>();

    static {
        for (MedicalProviderTypeEnum type: MedicalProviderTypeEnum.values()) {
            mapping.put(type.name(), type);
        }
    }

    public static MedicalProviderTypeEnum resolve(String name) {
        if (name == null) return null;
        return mapping.getOrDefault(name, null);
    }

    public boolean matches(String name) {
        return (this == resolve(name));
    }
}
