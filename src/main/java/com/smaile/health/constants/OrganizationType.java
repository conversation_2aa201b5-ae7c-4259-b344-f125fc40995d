package com.smaile.health.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum OrganizationType {

    SUPER_SMAILE("SMAILE", "Super Smaile"),
    IC("IC", "IC under <PERSON>maile"),
    IC_TPA("IC_TPA", "TPA under IC"),
    IC_MP("IC_MP", "MP under IC"),
    IC_TPA_MP("IC_TPA_MP", "MP under IC TPA"),
    SMAILE_TPA("SMAILE_TPA", "TPA under <PERSON>maile"),
    SMAILE_MP("SMAILE_MP", "MP under Smaile"),
    SMAILE_TPA_MP("SMAILE_TPA_MP", "MP under Smaile TPA"),
    PROFESSIONAL("PROFESSIONAL", "Professional");

    private static final Map<String, OrganizationType> mappings = new HashMap<>();

    static {
        for (OrganizationType type : values()) {
            mappings.put(type.code, type);
        }
    }

    private final String code;
    private final String description;

    public static OrganizationType resolve(String type) {
        if (type == null) {
            return null;
        }
        return mappings.get(type);
    }

    public boolean matches(String type) {
        return (this == resolve(type));
    }

}
