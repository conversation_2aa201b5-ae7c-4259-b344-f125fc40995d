# Override the properties for test environment here

server:
  port: 8000

spring:
  autoconfigure:
    exclude: # exclude: the security configuration, oauth2 configuration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration

  datasource:
    url: ******************************************
    driverClassName: org.hsqldb.jdbc.JDBCDriver

  jpa:
    database-platform: org.hibernate.dialect.HSQLDialect
    hibernate:
      ddl-auto: create-drop
    defer-datasource-initialization: true

audit:
  enabled: true
  cache-enabled: true
